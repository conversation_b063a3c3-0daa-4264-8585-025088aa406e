#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试数据库连接
"""

import pymysql
import socket
import time
import sys

# 定义数据库连接参数
数据库配置 = {
    'host': '**************',
    'port': 3306,
    'user': 'root',
    'password': 'Yuyu6709.',
    'database': 'kami3162',
    'charset': 'utf8mb4',
    'connect_timeout': 30
}

def 测试端口():
    """测试数据库端口是否开放"""
    print(f"测试端口 {数据库配置['host']}:{数据库配置['port']} 是否开放...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        结果 = sock.connect_ex((数据库配置['host'], int(数据库配置['port'])))
        if 结果 == 0:
            print(f"✅ 端口连接成功: {数据库配置['port']}")
            return True
        else:
            print(f"❌ 端口连接失败: {数据库配置['port']} (错误代码: {结果})")
            return False
    except Exception as e:
        print(f"❌ 端口测试出错: {e}")
        return False
    finally:
        sock.close()

def 测试数据库连接():
    """测试数据库连接是否正常"""
    print(f"尝试连接数据库 {数据库配置['host']}...")
    
    连接 = None
    try:
        # 尝试连接
        print("连接中...")
        连接 = pymysql.connect(**数据库配置)
        
        # 测试查询
        with 连接.cursor() as 游标:
            print("执行测试查询...")
            游标.execute("SELECT VERSION()")
            版本 = 游标.fetchone()
            print(f"✅ MySQL 版本: {版本[0]}")
            
            # 检查卡密系统表
            print("检查卡密系统表...")
            游标.execute("SHOW TABLES LIKE '卡密系统'")
            表存在 = 游标.fetchone()
            
            if 表存在:
                print("✅ 卡密系统表存在")
                
                # 查询表结构
                print("查询表结构...")
                游标.execute("DESCRIBE `卡密系统`")
                字段列表 = 游标.fetchall()
                print(f"✅ 表结构获取成功，共有 {len(字段列表)} 个字段")
                
                # 显示字段
                print("字段列表:")
                for 字段 in 字段列表[:5]:
                    print(f"  - {字段[0]} ({字段[1]})")
            else:
                print("❌ 卡密系统表不存在")
        
        print("✅ 数据库连接测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    finally:
        if 连接:
            连接.close()
            print("数据库连接已关闭")

if __name__ == "__main__":
    print("=" * 50)
    print("数据库连接测试")
    print("=" * 50)
    
    # 先测试端口
    if 测试端口():
        print("\n端口测试通过，开始测试数据库连接...")
        测试数据库连接()
    else:
        print("\n❌ 端口测试失败，无法连接到数据库服务器")
    
    print("=" * 50) 
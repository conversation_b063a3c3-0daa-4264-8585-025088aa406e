# 打包命令: pyinstaller --onefile --windowed --name="邮箱数据管理系统" email_tables_pyside6.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱表数据查看GUI界面 - PySide6版本
显示邮箱系统和邮箱免费表的最新100条数据
"""

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QTableWidget, QTableWidgetItem,
                               QPushButton, QLabel, QFrame, QHeaderView,
                               QMessageBox, QStatusBar, QSplitter)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QPalette, QColor, QIcon
import mysql.connector
from mysql.connector import Error
from datetime import datetime

class DatabaseWorker(QThread):
    """数据库操作工作线程"""
    data_loaded = Signal(list, int, list, int, list, int, dict)  # system_data, system_count, free_data, free_count, deleted_data, deleted_count, time_stats
    error_occurred = Signal(str)

    def __init__(self, db_config):
        super().__init__()
        self.db_config = db_config

    def run(self):
        """在线程中执行数据库查询"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor()

            # 获取邮箱系统表数据
            cursor.execute("""
                SELECT id, 邮箱, 创建时间, 人数
                FROM `邮箱系统`
                ORDER BY 创建时间 DESC
                LIMIT 100
            """)
            system_data = cursor.fetchall()

            # 获取邮箱系统表总数
            cursor.execute("SELECT COUNT(*) FROM `邮箱系统`")
            system_count = cursor.fetchone()[0]

            # 获取邮箱免费表数据
            cursor.execute("""
                SELECT id, 邮箱, 创建时间, 人数
                FROM `邮箱免费`
                ORDER BY 创建时间 DESC
                LIMIT 100
            """)
            free_data = cursor.fetchall()

            # 获取邮箱免费表总数
            cursor.execute("SELECT COUNT(*) FROM `邮箱免费`")
            free_count = cursor.fetchone()[0]

            # 获取已删除邮箱表数据
            cursor.execute("""
                SELECT 邮箱, 创建时间, 删除原因
                FROM `已删除邮箱`
                ORDER BY 创建时间 DESC
                LIMIT 100
            """)
            deleted_data = cursor.fetchall()

            # 获取已删除邮箱表总数
            cursor.execute("SELECT COUNT(*) FROM `已删除邮箱`")
            deleted_count = cursor.fetchone()[0]

            # 获取时间段统计
            time_stats = self.get_time_statistics(cursor)

            self.data_loaded.emit(system_data, system_count, free_data, free_count, deleted_data, deleted_count, time_stats)

        except Error as e:
            self.error_occurred.emit(f"数据库查询错误: {str(e)}")
        except Exception as e:
            self.error_occurred.emit(f"未知错误: {str(e)}")
        finally:
            if 'connection' in locals() and connection.is_connected():
                connection.close()

    def get_time_statistics(self, cursor):
        """获取时间段统计（排除当前这一分钟）"""
        time_stats = {
            'system_1min': 0,
            'system_2min': 0,
            'system_3min': 0,
            'system_10min_avg': 0,
            'free_1min': 0,
            'free_2min': 0,
            'free_3min': 0,
            'free_10min_avg': 0,
            'deleted_1min': 0,
            'deleted_2min': 0,
            'deleted_3min': 0,
            'deleted_today': 0,
            'deleted_10min_avg': 0
        }

        try:
            # 邮箱系统表 - 上一分钟（上一个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `邮箱系统`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['system_1min'] = cursor.fetchone()[0]

            # 邮箱系统表 - 上两分钟（上上个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `邮箱系统`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MINUTE), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['system_2min'] = cursor.fetchone()[0]

            # 邮箱系统表 - 上三分钟（上上上个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `邮箱系统`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MINUTE), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['system_3min'] = cursor.fetchone()[0]

            # 邮箱免费表 - 上一分钟（上一个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `邮箱免费`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['free_1min'] = cursor.fetchone()[0]

            # 邮箱免费表 - 上两分钟（上上个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `邮箱免费`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MINUTE), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['free_2min'] = cursor.fetchone()[0]

            # 邮箱免费表 - 上三分钟（上上上个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `邮箱免费`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MINUTE), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['free_3min'] = cursor.fetchone()[0]

            # 已删除邮箱表 - 上一分钟（上一个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `已删除邮箱`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['deleted_1min'] = cursor.fetchone()[0]

            # 已删除邮箱表 - 上两分钟（上上个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `已删除邮箱`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MINUTE), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['deleted_2min'] = cursor.fetchone()[0]

            # 已删除邮箱表 - 上三分钟（上上上个完整分钟）
            cursor.execute("""
                SELECT COUNT(*) FROM `已删除邮箱`
                WHERE 创建时间 >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MINUTE), '%Y-%m-%d %H:%i:00')
                AND 创建时间 < DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MINUTE), '%Y-%m-%d %H:%i:00')
            """)
            time_stats['deleted_3min'] = cursor.fetchone()[0]

            # 已删除邮箱表 - 今天总数
            cursor.execute("""
                SELECT COUNT(*) FROM `已删除邮箱`
                WHERE DATE(创建时间) = CURDATE()
            """)
            time_stats['deleted_today'] = cursor.fetchone()[0]

            # 计算十分钟平均值（排除当前这一分钟）
            # 邮箱系统表 - 十分钟平均
            cursor.execute("""
                SELECT COUNT(*) FROM `邮箱系统`
                WHERE 创建时间 >= DATE_SUB(NOW(), INTERVAL 11 MINUTE)
                AND 创建时间 < DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            """)
            system_10min_total = cursor.fetchone()[0]
            time_stats['system_10min_avg'] = round(system_10min_total / 10, 1)

            # 邮箱免费表 - 十分钟平均
            cursor.execute("""
                SELECT COUNT(*) FROM `邮箱免费`
                WHERE 创建时间 >= DATE_SUB(NOW(), INTERVAL 11 MINUTE)
                AND 创建时间 < DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            """)
            free_10min_total = cursor.fetchone()[0]
            time_stats['free_10min_avg'] = round(free_10min_total / 10, 1)

            # 已删除邮箱表 - 十分钟平均
            cursor.execute("""
                SELECT COUNT(*) FROM `已删除邮箱`
                WHERE 创建时间 >= DATE_SUB(NOW(), INTERVAL 11 MINUTE)
                AND 创建时间 < DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            """)
            deleted_10min_total = cursor.fetchone()[0]
            time_stats['deleted_10min_avg'] = round(deleted_10min_total / 10, 1)

        except Exception as e:
            print(f"时间统计查询错误: {e}")

        return time_stats

class EmailTablesGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("邮箱表数据查看器 - PySide6")
        self.setGeometry(100, 100, 1400, 800)

        # 数据库配置
        self.db_config = {
            'host': '**************',
            'port': 3306,
            'user': 'root',
            'password': 'Yuyu6709.',
            'database': 'kami3162',
            'charset': 'utf8mb4',
            'autocommit': True
        }

        self.setup_ui()
        self.setup_styles()
        self.load_data()

        # 设置自动刷新定时器（可选）
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.load_data)
        # self.auto_refresh_timer.start(30000)  # 30秒自动刷新（可取消注释启用）

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 标题和控制区域
        header_layout = QHBoxLayout()

        # 标题
        title_label = QLabel("📊 邮箱表数据管理系统")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; padding: 10px;")

        # 刷新按钮
        self.refresh_button = QPushButton("🔄 刷新数据")
        self.refresh_button.setFont(QFont("Arial", 10))
        self.refresh_button.clicked.connect(self.load_data)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.refresh_button)
        main_layout.addLayout(header_layout)

        # 简单的总计统计
        total_stats_layout = QHBoxLayout()

        total_label = QLabel("总计:")
        total_label.setFont(QFont("Arial", 12, QFont.Bold))
        total_label.setStyleSheet("color: #2c3e50; padding: 5px;")

        self.total_stats_label = QLabel("0")
        self.total_stats_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.total_stats_label.setStyleSheet("color: #e74c3c; padding: 5px;")

        total_stats_layout.addWidget(total_label)
        total_stats_layout.addWidget(self.total_stats_label)
        total_stats_layout.addStretch()

        main_layout.addLayout(total_stats_layout)

        # 表格区域
        tables_layout = QHBoxLayout()

        # 邮箱系统表区域
        system_group = QFrame()
        system_group.setFrameStyle(QFrame.StyledPanel)
        system_layout = QVBoxLayout(system_group)

        # 系统表标题和总数
        system_header_layout = QHBoxLayout()
        system_title = QLabel("📧 邮箱系统表")
        system_title.setFont(QFont("Arial", 12, QFont.Bold))
        system_title.setStyleSheet("color: #3498db; padding: 5px;")

        system_count_label = QLabel("总数:")
        system_count_label.setFont(QFont("Arial", 10))
        system_count_label.setStyleSheet("color: #7f8c8d; padding: 3px;")

        self.system_stats_label = QLabel("0")
        self.system_stats_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.system_stats_label.setStyleSheet("color: #3498db; padding: 3px;")

        system_header_layout.addWidget(system_title)
        system_header_layout.addStretch()
        system_header_layout.addWidget(system_count_label)
        system_header_layout.addWidget(self.system_stats_label)

        # 系统表十分钟平均
        system_avg_layout = QHBoxLayout()

        sys_avg_label = QLabel("10分钟平均:")
        sys_avg_label.setFont(QFont("Arial", 9))
        sys_avg_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.system_10min_avg = QLabel("0.0")
        self.system_10min_avg.setFont(QFont("Arial", 9, QFont.Bold))
        self.system_10min_avg.setStyleSheet("color: #3498db; padding: 2px;")

        system_avg_layout.addWidget(sys_avg_label)
        system_avg_layout.addWidget(self.system_10min_avg)
        system_avg_layout.addStretch()

        # 系统表时间段统计
        system_time_layout = QHBoxLayout()

        sys_1min_label = QLabel("上1分钟:")
        sys_1min_label.setFont(QFont("Arial", 9))
        sys_1min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.system_1min_stats = QLabel("0")
        self.system_1min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.system_1min_stats.setStyleSheet("color: #3498db; padding: 2px;")

        sys_2min_label = QLabel("上2分钟:")
        sys_2min_label.setFont(QFont("Arial", 9))
        sys_2min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.system_2min_stats = QLabel("0")
        self.system_2min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.system_2min_stats.setStyleSheet("color: #3498db; padding: 2px;")

        sys_3min_label = QLabel("上3分钟:")
        sys_3min_label.setFont(QFont("Arial", 9))
        sys_3min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.system_3min_stats = QLabel("0")
        self.system_3min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.system_3min_stats.setStyleSheet("color: #3498db; padding: 2px;")

        system_time_layout.addWidget(sys_1min_label)
        system_time_layout.addWidget(self.system_1min_stats)
        system_time_layout.addWidget(QLabel("|"))
        system_time_layout.addWidget(sys_2min_label)
        system_time_layout.addWidget(self.system_2min_stats)
        system_time_layout.addWidget(QLabel("|"))
        system_time_layout.addWidget(sys_3min_label)
        system_time_layout.addWidget(self.system_3min_stats)
        system_time_layout.addStretch()

        # 系统表格
        self.system_table = QTableWidget()
        self.system_table.setColumnCount(2)
        self.system_table.setHorizontalHeaderLabels(["邮箱", "创建时间"])
        self.setup_table_style(self.system_table)

        system_layout.addLayout(system_header_layout)
        system_layout.addLayout(system_avg_layout)
        system_layout.addLayout(system_time_layout)
        system_layout.addWidget(self.system_table)

        # 邮箱免费表区域
        free_group = QFrame()
        free_group.setFrameStyle(QFrame.StyledPanel)
        free_layout = QVBoxLayout(free_group)

        # 免费表标题和总数
        free_header_layout = QHBoxLayout()
        free_title = QLabel("🆓 邮箱免费表")
        free_title.setFont(QFont("Arial", 12, QFont.Bold))
        free_title.setStyleSheet("color: #27ae60; padding: 5px;")

        free_count_label = QLabel("总数:")
        free_count_label.setFont(QFont("Arial", 10))
        free_count_label.setStyleSheet("color: #7f8c8d; padding: 3px;")

        self.free_stats_label = QLabel("0")
        self.free_stats_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.free_stats_label.setStyleSheet("color: #27ae60; padding: 3px;")

        free_header_layout.addWidget(free_title)
        free_header_layout.addStretch()
        free_header_layout.addWidget(free_count_label)
        free_header_layout.addWidget(self.free_stats_label)

        # 免费表十分钟平均
        free_avg_layout = QHBoxLayout()

        free_avg_label = QLabel("10分钟平均:")
        free_avg_label.setFont(QFont("Arial", 9))
        free_avg_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.free_10min_avg = QLabel("0.0")
        self.free_10min_avg.setFont(QFont("Arial", 9, QFont.Bold))
        self.free_10min_avg.setStyleSheet("color: #27ae60; padding: 2px;")

        free_avg_layout.addWidget(free_avg_label)
        free_avg_layout.addWidget(self.free_10min_avg)
        free_avg_layout.addStretch()

        # 免费表时间段统计
        free_time_layout = QHBoxLayout()

        free_1min_label = QLabel("上1分钟:")
        free_1min_label.setFont(QFont("Arial", 9))
        free_1min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.free_1min_stats = QLabel("0")
        self.free_1min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.free_1min_stats.setStyleSheet("color: #27ae60; padding: 2px;")

        free_2min_label = QLabel("上2分钟:")
        free_2min_label.setFont(QFont("Arial", 9))
        free_2min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.free_2min_stats = QLabel("0")
        self.free_2min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.free_2min_stats.setStyleSheet("color: #27ae60; padding: 2px;")

        free_3min_label = QLabel("上3分钟:")
        free_3min_label.setFont(QFont("Arial", 9))
        free_3min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.free_3min_stats = QLabel("0")
        self.free_3min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.free_3min_stats.setStyleSheet("color: #27ae60; padding: 2px;")

        free_time_layout.addWidget(free_1min_label)
        free_time_layout.addWidget(self.free_1min_stats)
        free_time_layout.addWidget(QLabel("|"))
        free_time_layout.addWidget(free_2min_label)
        free_time_layout.addWidget(self.free_2min_stats)
        free_time_layout.addWidget(QLabel("|"))
        free_time_layout.addWidget(free_3min_label)
        free_time_layout.addWidget(self.free_3min_stats)
        free_time_layout.addStretch()

        # 免费表格
        self.free_table = QTableWidget()
        self.free_table.setColumnCount(2)
        self.free_table.setHorizontalHeaderLabels(["邮箱", "创建时间"])
        self.setup_table_style(self.free_table)

        free_layout.addLayout(free_header_layout)
        free_layout.addLayout(free_avg_layout)
        free_layout.addLayout(free_time_layout)
        free_layout.addWidget(self.free_table)

        # 已删除邮箱表区域
        deleted_group = QFrame()
        deleted_group.setFrameStyle(QFrame.StyledPanel)
        deleted_layout = QVBoxLayout(deleted_group)

        # 已删除表标题和总数
        deleted_header_layout = QHBoxLayout()
        deleted_title = QLabel("🗑️ 已删除邮箱表")
        deleted_title.setFont(QFont("Arial", 12, QFont.Bold))
        deleted_title.setStyleSheet("color: #e74c3c; padding: 5px;")

        deleted_count_label = QLabel("总数:")
        deleted_count_label.setFont(QFont("Arial", 10))
        deleted_count_label.setStyleSheet("color: #7f8c8d; padding: 3px;")

        self.deleted_stats_label = QLabel("0")
        self.deleted_stats_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.deleted_stats_label.setStyleSheet("color: #e74c3c; padding: 3px;")

        deleted_header_layout.addWidget(deleted_title)
        deleted_header_layout.addStretch()
        deleted_header_layout.addWidget(deleted_count_label)
        deleted_header_layout.addWidget(self.deleted_stats_label)

        # 已删除表十分钟平均
        deleted_avg_layout = QHBoxLayout()

        del_avg_label = QLabel("10分钟平均:")
        del_avg_label.setFont(QFont("Arial", 9))
        del_avg_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.deleted_10min_avg = QLabel("0.0")
        self.deleted_10min_avg.setFont(QFont("Arial", 9, QFont.Bold))
        self.deleted_10min_avg.setStyleSheet("color: #e74c3c; padding: 2px;")

        deleted_avg_layout.addWidget(del_avg_label)
        deleted_avg_layout.addWidget(self.deleted_10min_avg)
        deleted_avg_layout.addStretch()

        # 已删除表时间段统计
        deleted_time_layout = QHBoxLayout()

        del_1min_label = QLabel("上1分钟:")
        del_1min_label.setFont(QFont("Arial", 9))
        del_1min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.deleted_1min_stats = QLabel("0")
        self.deleted_1min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.deleted_1min_stats.setStyleSheet("color: #e74c3c; padding: 2px;")

        del_2min_label = QLabel("上2分钟:")
        del_2min_label.setFont(QFont("Arial", 9))
        del_2min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.deleted_2min_stats = QLabel("0")
        self.deleted_2min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.deleted_2min_stats.setStyleSheet("color: #e74c3c; padding: 2px;")

        del_3min_label = QLabel("上3分钟:")
        del_3min_label.setFont(QFont("Arial", 9))
        del_3min_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.deleted_3min_stats = QLabel("0")
        self.deleted_3min_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.deleted_3min_stats.setStyleSheet("color: #e74c3c; padding: 2px;")

        del_today_label = QLabel("今天:")
        del_today_label.setFont(QFont("Arial", 9))
        del_today_label.setStyleSheet("color: #7f8c8d; padding: 2px;")

        self.deleted_today_stats = QLabel("0")
        self.deleted_today_stats.setFont(QFont("Arial", 9, QFont.Bold))
        self.deleted_today_stats.setStyleSheet("color: #e74c3c; padding: 2px;")

        deleted_time_layout.addWidget(del_1min_label)
        deleted_time_layout.addWidget(self.deleted_1min_stats)
        deleted_time_layout.addWidget(QLabel("|"))
        deleted_time_layout.addWidget(del_2min_label)
        deleted_time_layout.addWidget(self.deleted_2min_stats)
        deleted_time_layout.addWidget(QLabel("|"))
        deleted_time_layout.addWidget(del_3min_label)
        deleted_time_layout.addWidget(self.deleted_3min_stats)
        deleted_time_layout.addWidget(QLabel("|"))
        deleted_time_layout.addWidget(del_today_label)
        deleted_time_layout.addWidget(self.deleted_today_stats)
        deleted_time_layout.addStretch()

        # 已删除表格
        self.deleted_table = QTableWidget()
        self.deleted_table.setColumnCount(3)
        self.deleted_table.setHorizontalHeaderLabels(["邮箱", "创建时间", "删除原因"])
        self.setup_deleted_table_style(self.deleted_table)

        deleted_layout.addLayout(deleted_header_layout)
        deleted_layout.addLayout(deleted_avg_layout)
        deleted_layout.addLayout(deleted_time_layout)
        deleted_layout.addWidget(self.deleted_table)

        tables_layout.addWidget(system_group)
        tables_layout.addWidget(free_group)
        tables_layout.addWidget(deleted_group)
        main_layout.addLayout(tables_layout)

        # 状态栏
        self.status_bar = QStatusBar()
        self.status_bar.showMessage("准备就绪")
        self.setStatusBar(self.status_bar)

    def setup_table_style(self, table):
        """设置表格样式"""
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.setSortingEnabled(True)

        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 邮箱列自适应
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # 创建时间列固定宽度

        table.setColumnWidth(1, 150)  # 创建时间

        # 简单的表格样式
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #3498db;
                selection-color: white;
                color: #333;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #eee;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_deleted_table_style(self, table):
        """设置已删除邮箱表格样式"""
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.setSortingEnabled(True)

        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 邮箱列自适应
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # 创建时间列固定宽度
        header.setSectionResizeMode(2, QHeaderView.Fixed)   # 删除原因列固定宽度

        table.setColumnWidth(1, 150)  # 创建时间
        table.setColumnWidth(2, 120)  # 删除原因

        # 简单的表格样式
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e74c3c;
                selection-color: white;
                color: #333;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #eee;
            }
            QHeaderView::section {
                background-color: #c0392b;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_styles(self):
        """设置应用程序样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
        """)

    def load_data(self):
        """加载数据"""
        self.status_bar.showMessage("正在加载数据...")
        self.refresh_button.setEnabled(False)
        self.refresh_button.setText("加载中...")

        # 创建工作线程
        self.worker = DatabaseWorker(self.db_config)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.error_occurred.connect(self.on_error_occurred)
        self.worker.finished.connect(self.on_loading_finished)
        self.worker.start()

    def on_data_loaded(self, system_data, system_count, free_data, free_count, deleted_data, deleted_count, time_stats):
        """数据加载完成"""
        # 更新总数统计信息（不包含已删除邮箱）
        self.system_stats_label.setText(f"{system_count:,}")
        self.free_stats_label.setText(f"{free_count:,}")
        self.total_stats_label.setText(f"{system_count + free_count:,}")

        # 更新已删除邮箱统计信息
        self.deleted_stats_label.setText(f"{deleted_count:,}")

        # 更新时间段统计信息
        self.system_1min_stats.setText(f"{time_stats['system_1min']:,}")
        self.free_1min_stats.setText(f"{time_stats['free_1min']:,}")
        self.system_2min_stats.setText(f"{time_stats['system_2min']:,}")
        self.free_2min_stats.setText(f"{time_stats['free_2min']:,}")
        self.system_3min_stats.setText(f"{time_stats['system_3min']:,}")
        self.free_3min_stats.setText(f"{time_stats['free_3min']:,}")

        # 更新已删除邮箱时间段统计
        self.deleted_1min_stats.setText(f"{time_stats['deleted_1min']:,}")
        self.deleted_2min_stats.setText(f"{time_stats['deleted_2min']:,}")
        self.deleted_3min_stats.setText(f"{time_stats['deleted_3min']:,}")
        self.deleted_today_stats.setText(f"{time_stats['deleted_today']:,}")

        # 更新十分钟平均值
        self.system_10min_avg.setText(f"{time_stats['system_10min_avg']}")
        self.free_10min_avg.setText(f"{time_stats['free_10min_avg']}")
        self.deleted_10min_avg.setText(f"{time_stats['deleted_10min_avg']}")

        # 更新邮箱系统表
        self.update_table(self.system_table, system_data)

        # 更新邮箱免费表
        self.update_table(self.free_table, free_data)

        # 更新已删除邮箱表
        self.update_deleted_table(self.deleted_table, deleted_data)

        # 更新状态栏
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        total_1min = time_stats['system_1min'] + time_stats['free_1min']
        total_2min = time_stats['system_2min'] + time_stats['free_2min']
        total_3min = time_stats['system_3min'] + time_stats['free_3min']
        self.status_bar.showMessage(f"✅ 数据加载完成 | 总计: {system_count + free_count:,} | 已删除: {deleted_count:,} | 上1分钟: {total_1min} | 上2分钟: {total_2min} | 上3分钟: {total_3min} | 更新时间: {current_time}")

    def update_table(self, table, data):
        """更新表格数据"""
        table.setRowCount(len(data))

        for row_idx, row_data in enumerate(data):
            # 邮箱
            table.setItem(row_idx, 0, QTableWidgetItem(str(row_data[1])))

            # 创建时间
            create_time = row_data[2].strftime('%Y-%m-%d %H:%M:%S') if row_data[2] else 'N/A'
            table.setItem(row_idx, 1, QTableWidgetItem(create_time))

            # 设置创建时间列居中对齐
            time_item = table.item(row_idx, 1)
            if time_item:
                time_item.setTextAlignment(Qt.AlignCenter)

    def update_deleted_table(self, table, data):
        """更新已删除邮箱表格数据"""
        table.setRowCount(len(data))

        for row_idx, row_data in enumerate(data):
            # 邮箱
            table.setItem(row_idx, 0, QTableWidgetItem(str(row_data[0])))

            # 创建时间
            create_time = row_data[1].strftime('%Y-%m-%d %H:%M:%S') if row_data[1] else 'N/A'
            table.setItem(row_idx, 1, QTableWidgetItem(create_time))

            # 删除原因
            table.setItem(row_idx, 2, QTableWidgetItem(str(row_data[2]) if row_data[2] else '未知'))

            # 设置创建时间和删除原因列居中对齐
            time_item = table.item(row_idx, 1)
            if time_item:
                time_item.setTextAlignment(Qt.AlignCenter)

            reason_item = table.item(row_idx, 2)
            if reason_item:
                reason_item.setTextAlignment(Qt.AlignCenter)

    def on_error_occurred(self, error_message):
        """处理错误"""
        QMessageBox.critical(self, "错误", error_message)
        self.status_bar.showMessage("数据加载失败")

    def on_loading_finished(self):
        """加载完成"""
        self.refresh_button.setEnabled(True)
        self.refresh_button.setText("🔄 刷新数据")

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("邮箱表数据查看器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("数据管理工具")

    # 创建主窗口
    window = EmailTablesGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()

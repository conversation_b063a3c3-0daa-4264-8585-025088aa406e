import sys
import json
import os
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QMessageBox, QGridLayout, QSizePolicy, QCheckBox)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QIcon

class LoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("登录")
        self.setMinimumSize(350, 250)

        # 主控件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # 标题
        title_label = QLabel("用户登录")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 表单布局
        form_layout = QGridLayout()
        form_layout.setVerticalSpacing(8)

        # 用户名
        form_layout.addWidget(QLabel("用户名:"), 0, 0)
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        form_layout.addWidget(self.username_edit, 0, 1)

        # 密码
        form_layout.addWidget(QLabel("密码:"), 1, 0)
        
        # 密码输入框和可见性切换按钮的容器
        password_container = QHBoxLayout()
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码")
        self.password_edit.setEchoMode(QLineEdit.Password)
        password_container.addWidget(self.password_edit)
        
        # 添加密码可见性切换按钮
        self.toggle_password_btn = QPushButton("👁")
        self.toggle_password_btn.setObjectName("toggle_password_btn")
        self.toggle_password_btn.setFixedWidth(30)
        self.toggle_password_btn.setToolTip("显示/隐藏密码")
        self.toggle_password_btn.clicked.connect(self.toggle_password_visibility)
        password_container.addWidget(self.toggle_password_btn)
        
        form_layout.addLayout(password_container, 1, 1)
        
        # 添加记住密码复选框
        self.remember_password = QCheckBox("记住密码")
        form_layout.addWidget(self.remember_password, 2, 1)

        main_layout.addLayout(form_layout)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        self.login_button = QPushButton("登录")
        self.reset_button = QPushButton("重置")
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.reset_button)
        main_layout.addLayout(button_layout)
        
        main_layout.addStretch()

        # 连接信号和槽
        self.login_button.clicked.connect(self.handle_login)
        self.reset_button.clicked.connect(self.reset_fields)

        # 应用一个简洁的样式表
        self.apply_stylesheet()
        
        # 加载保存的密码（如果有）
        self.load_saved_password()

    def handle_login(self):
        username = self.username_edit.text()
        password = self.password_edit.text()

        if not username or not password:
            QMessageBox.warning(self, "输入错误", "用户名和密码不能为空！")
            return

        # 简单的硬编码验证
        if username == "admin" and password == "password":
            # 保存密码（如果选择了记住密码）
            if self.remember_password.isChecked():
                self.save_password(username, password)
            
            # 登录成功动画
            self.login_button.setEnabled(False)
            self.login_button.setText("登录成功!")
            
            # 延迟关闭窗口
            QTimer.singleShot(1000, lambda: self.close())
            
            QMessageBox.information(self, "登录成功", f"欢迎 {username}！")
        else:
            QMessageBox.warning(self, "登录失败", "用户名或密码错误。")

    def reset_fields(self):
        self.username_edit.clear()
        self.password_edit.clear()
        self.username_edit.setFocus()

    def apply_stylesheet(self):
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
                font-family: 'Microsoft YaHei', sans-serif;
                color: #333333;
            }
            QLabel {
                font-size: 14px;
            }
            QLineEdit {
                padding: 6px;
                border: 1px solid #ccc;
                border-radius: 3px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 1px solid #0078d7;
            }
            QPushButton {
                background-color: #0078d7;
                color: white;
                border-radius: 3px;
                padding: 6px 12px;
                min-height: 28px;
                font-size: 14px;
                border: none;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:pressed {
                background-color: #004578;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QCheckBox {
                font-size: 13px;
                color: #555555;
            }
            QCheckBox::indicator {
                width: 13px;
                height: 13px;
                border: 1px solid #999999;
                border-radius: 2px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d7;
                border-color: #0078d7;
            }
            #toggle_password_btn {
                background-color: #e0e0e0;
                color: #333333;
                border-radius: 3px;
                padding: 4px;
                min-height: 24px;
                font-size: 12px;
            }
            #toggle_password_btn:hover {
                background-color: #d0d0d0;
            }
        """)

    def toggle_password_visibility(self):
        """切换密码可见性"""
        if self.password_edit.echoMode() == QLineEdit.Password:
            self.password_edit.setEchoMode(QLineEdit.Normal)
            self.toggle_password_btn.setText("🔒")
        else:
            self.password_edit.setEchoMode(QLineEdit.Password)
            self.toggle_password_btn.setText("👁")

    def save_password(self, username, password):
        """保存用户名和密码"""
        data = {
            "username": username,
            "password": password
        }
        try:
            with open("login_info.json", "w") as f:
                json.dump(data, f)
        except Exception as e:
            print(f"保存密码失败: {e}")
    
    def load_saved_password(self):
        """加载保存的用户名和密码"""
        try:
            if os.path.exists("login_info.json"):
                with open("login_info.json", "r") as f:
                    data = json.load(f)
                    self.username_edit.setText(data.get("username", ""))
                    self.password_edit.setText(data.get("password", ""))
                    self.remember_password.setChecked(True)
        except Exception as e:
            print(f"加载密码失败: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = LoginWindow()
    window.show()
    sys.exit(app.exec()) 
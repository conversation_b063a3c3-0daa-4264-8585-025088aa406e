#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨数据库数据迁移 - 从源数据库移动1万条数据到目标数据库
从邮箱系统表移动到另一个数据库的邮箱系统表
"""

import mysql.connector
from mysql.connector import Error
import time

def cross_database_migration():
    """跨数据库迁移，移动1万条数据"""
    
    # 源数据库配置
    source_config = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': False
    }
    
    # 目标数据库配置
    target_config = {
        'host': '***************',
        'port': 3306,
        'user': 'root',
        'password': 'YU6709',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': False
    }
    
    # 迁移数量
    MIGRATION_LIMIT = 10000
    
    source_conn = None
    target_conn = None
    
    try:
        print("正在连接源数据库...")
        source_conn = mysql.connector.connect(**source_config)
        
        print("正在连接目标数据库...")
        target_conn = mysql.connector.connect(**target_config)
        
        if source_conn.is_connected() and target_conn.is_connected():
            print("✅ 数据库连接成功!")
            
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            print("\n" + "="*60)
            print("跨数据库数据迁移执行")
            print(f"迁移数量: {MIGRATION_LIMIT:,} 条")
            print("="*60)
            
            # 1. 获取源数据库数据量
            print("1. 📊 检查数据状态...")
            source_cursor.execute("SELECT COUNT(*) FROM `邮箱系统`")
            source_total = source_cursor.fetchone()[0]
            
            target_cursor.execute("SELECT COUNT(*) FROM `邮箱系统`")
            target_count_before = target_cursor.fetchone()[0]
            
            print(f"   源数据库邮箱系统: {source_total:,} 条")
            print(f"   目标数据库邮箱系统: {target_count_before:,} 条")
            
            if source_total == 0:
                print("❌ 源数据库中没有数据需要迁移")
                return False
            
            # 确定实际迁移数量
            actual_migration_count = min(MIGRATION_LIMIT, source_total)
            
            print(f"\n2. 📋 迁移计划:")
            print(f"   计划迁移: {MIGRATION_LIMIT:,} 条")
            print(f"   实际迁移: {actual_migration_count:,} 条")
            
            # 3. 获取表结构信息
            print("\n3. 🔍 获取表结构...")
            source_cursor.execute("DESCRIBE `邮箱系统`")
            columns_info = source_cursor.fetchall()
            
            # 构建列名列表
            column_names = [col[0] for col in columns_info]
            columns_str = ", ".join([f"`{col}`" for col in column_names])
            placeholders = ", ".join(["%s"] * len(column_names))
            
            print(f"   表字段数: {len(column_names)} 个")
            
            # 4. 确认迁移
            print(f"\n⚠️  即将迁移 {actual_migration_count:,} 条数据")
            print(f"   源数据库: {source_config['host']}")
            print(f"   目标数据库: {target_config['host']}")
            
            confirm = input("\n确认执行迁移? (输入 'YES' 确认): ").strip()
            
            if confirm != 'YES':
                print("❌ 迁移已取消")
                return False
            
            # 5. 开始迁移
            print(f"\n🚀 开始执行数据迁移...")
            start_time = time.time()
            
            # 分批处理，每批1000条
            batch_size = 1000
            total_migrated = 0
            migrated_ids = []
            
            print(f"   使用批处理模式，每批 {batch_size} 条")
            
            for offset in range(0, actual_migration_count, batch_size):
                current_batch_size = min(batch_size, actual_migration_count - offset)
                
                print(f"\n   处理批次 {offset//batch_size + 1}: {offset + 1} - {offset + current_batch_size}")
                
                # 获取一批数据
                batch_start = time.time()
                source_cursor.execute(f"SELECT {columns_str} FROM `邮箱系统` LIMIT %s OFFSET %s", 
                                    (current_batch_size, offset))
                batch_data = source_cursor.fetchall()
                
                if not batch_data:
                    print("   ⚠️  没有更多数据")
                    break
                
                # 插入到目标数据库
                insert_query = f"INSERT INTO `邮箱系统` ({columns_str}) VALUES ({placeholders})"
                target_cursor.executemany(insert_query, batch_data)
                target_conn.commit()
                
                # 记录已迁移的数据（假设第一列是ID）
                if column_names[0].lower() in ['id', 'email_id', 'mail_id']:
                    batch_ids = [row[0] for row in batch_data]
                    migrated_ids.extend(batch_ids)
                
                total_migrated += len(batch_data)
                batch_time = time.time() - batch_start
                
                print(f"   ✅ 批次完成: {len(batch_data)} 条 (耗时: {batch_time:.3f}秒)")
                
                # 显示进度
                progress = (total_migrated / actual_migration_count) * 100
                print(f"   📈 总进度: {total_migrated:,}/{actual_migration_count:,} ({progress:.1f}%)")
            
            # 6. 删除源数据库中已迁移的数据
            print(f"\n🗑️  删除源数据库中已迁移的数据...")
            delete_start = time.time()
            
            if migrated_ids and column_names[0].lower() in ['id', 'email_id', 'mail_id']:
                # 分批删除
                delete_batch_size = 1000
                total_deleted = 0
                
                for i in range(0, len(migrated_ids), delete_batch_size):
                    batch_ids = migrated_ids[i:i + delete_batch_size]
                    placeholders_delete = ", ".join(["%s"] * len(batch_ids))
                    
                    delete_query = f"DELETE FROM `邮箱系统` WHERE `{column_names[0]}` IN ({placeholders_delete})"
                    source_cursor.execute(delete_query, batch_ids)
                    
                    total_deleted += source_cursor.rowcount
                    
                    print(f"   删除批次: {total_deleted:,}/{len(migrated_ids):,}")
                
                source_conn.commit()
                delete_time = time.time() - delete_start
                print(f"   ✅ 删除完成: {total_deleted:,} 条 (耗时: {delete_time:.3f}秒)")
            else:
                # 如果无法通过ID删除，则删除前N条记录
                source_cursor.execute(f"DELETE FROM `邮箱系统` LIMIT %s", (total_migrated,))
                source_conn.commit()
                delete_time = time.time() - delete_start
                print(f"   ✅ 删除完成: {source_cursor.rowcount:,} 条 (耗时: {delete_time:.3f}秒)")
            
            # 7. 最终验证
            print(f"\n📋 最终验证...")
            
            source_cursor.execute("SELECT COUNT(*) FROM `邮箱系统`")
            source_count_after = source_cursor.fetchone()[0]
            
            target_cursor.execute("SELECT COUNT(*) FROM `邮箱系统`")
            target_count_after = target_cursor.fetchone()[0]
            
            total_time = time.time() - start_time
            
            print(f"\n" + "="*60)
            print("🎉 迁移完成!")
            print("="*60)
            
            print(f"📊 迁移结果:")
            print(f"   源数据库: {source_total:,} → {source_count_after:,} 条")
            print(f"   目标数据库: {target_count_before:,} → {target_count_after:,} 条")
            print(f"   实际迁移: {total_migrated:,} 条")
            
            print(f"\n⏱️  性能统计:")
            print(f"   总耗时: {total_time:.3f}秒")
            print(f"   处理速度: {total_migrated/total_time:.0f} 条/秒")
            
            # 验证数据完整性
            expected_target_count = target_count_before + total_migrated
            if target_count_after == expected_target_count:
                print(f"\n✅ 迁移成功!")
                print(f"   ✅ 数据完整性验证通过")
                print(f"   ✅ {total_migrated:,} 条数据已成功迁移")
            else:
                print(f"\n⚠️  数据验证异常")
                print(f"   期望目标数量: {expected_target_count:,}")
                print(f"   实际目标数量: {target_count_after:,}")
            
            source_cursor.close()
            target_cursor.close()
            
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        if source_conn:
            try:
                source_conn.rollback()
            except:
                pass
        if target_conn:
            try:
                target_conn.rollback()
            except:
                pass
        return False
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
        if source_conn:
            try:
                source_conn.rollback()
                print("🔄 源数据库事务已回滚")
            except:
                pass
        if target_conn:
            try:
                target_conn.rollback()
                print("🔄 目标数据库事务已回滚")
            except:
                pass
        return False
        
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        if source_conn:
            try:
                source_conn.rollback()
            except:
                pass
        if target_conn:
            try:
                target_conn.rollback()
            except:
                pass
        return False
        
    finally:
        if source_conn and source_conn.is_connected():
            source_conn.close()
            print("\n源数据库连接已关闭")
        if target_conn and target_conn.is_connected():
            target_conn.close()
            print("目标数据库连接已关闭")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("跨数据库数据迁移工具")
    print("从源数据库移动1万条数据到目标数据库")
    print("=" * 60)
    
    success = cross_database_migration()
    
    if success:
        print("\n🎉 迁移成功完成!")
    else:
        print("\n❌ 迁移失败或被取消")

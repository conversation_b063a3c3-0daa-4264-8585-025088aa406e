#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移执行脚本
安全地将源数据库的数据迁移到目标数据库
"""

import pymysql
import logging
from datetime import datetime
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
源数据库配置 = {
    'host': '***************',
    'port': 3306,
    'user': 'root',
    'password': 'YU6709',
    'database': 'kami3162',
    'charset': 'utf8mb4'
}

目标数据库配置 = {
    'host': '**************',
    'port': 3306,
    'user': 'root',
    'password': 'Yuyu6709.',
    'database': 'kami3162',
    'charset': 'utf8mb4'
}

class DatabaseMigrator:
    def __init__(self, source_config, target_config):
        self.source_config = source_config
        self.target_config = target_config
        self.source_conn = None
        self.target_conn = None
        
        # 共同字段列表
        self.common_fields = [
            '上次获取时间', '上次重置时间', '二十分钟开始时间', '二十分钟调用次数', 
            '今日获取次数', '到期时间', '剩余额度', '卡密', '备注', '总调用次数', 
            '最大数量', '最近登录', '本月登录次数', '机器码', '机器码下线时间', 
            '机器码是否在线', '禁用卡密', '程序名', '类型', '调用次数', '首次登录时间'
        ]
    
    def connect_databases(self):
        """连接数据库"""
        try:
            logger.info("连接源数据库...")
            self.source_conn = pymysql.connect(**self.source_config)
            logger.info("源数据库连接成功")
            
            logger.info("连接目标数据库...")
            self.target_conn = pymysql.connect(**self.target_config)
            logger.info("目标数据库连接成功")
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def get_migration_preview(self):
        """预览将要迁移的数据"""
        try:
            with self.source_conn.cursor(pymysql.cursors.DictCursor) as source_cursor:
                with self.target_conn.cursor() as target_cursor:
                    # 查询源数据库中目标数据库没有的记录
                    preview_sql = """
                    SELECT COUNT(*) as count
                    FROM `卡密系统` s
                    WHERE s.`卡密` NOT IN (
                        SELECT `卡密` FROM `kami3162`.`卡密系统`
                    )
                    """
                    
                    # 在源数据库执行查询
                    source_cursor.execute(preview_sql)
                    result = source_cursor.fetchone()
                    new_records_count = result['count'] if result else 0
                    
                    # 获取一些示例记录
                    if new_records_count > 0:
                        sample_sql = """
                        SELECT `卡密`, `类型`, `程序名`, `到期时间`
                        FROM `卡密系统` s
                        WHERE s.`卡密` NOT IN (
                            SELECT `卡密` FROM `kami3162`.`卡密系统`
                        )
                        LIMIT 5
                        """
                        source_cursor.execute(sample_sql)
                        sample_records = source_cursor.fetchall()
                    else:
                        sample_records = []
                    
                    return new_records_count, sample_records
                    
        except Exception as e:
            logger.error(f"预览查询失败: {e}")
            return 0, []
    
    def execute_incremental_migration(self, dry_run=True):
        """执行增量迁移"""
        try:
            # 先预览
            new_count, samples = self.get_migration_preview()
            
            print(f"\n{'='*60}")
            print(f"📊 迁移预览报告")
            print(f"{'='*60}")
            print(f"将要迁移的新记录数: {new_count}")
            
            if new_count == 0:
                print("✅ 没有需要迁移的新记录，两个数据库已同步")
                return True
            
            print(f"\n📋 示例记录预览:")
            for i, record in enumerate(samples, 1):
                print(f"  {i}. 卡密: {record['卡密']}, 类型: {record['类型']}, "
                      f"程序: {record['程序名']}, 到期: {record['到期时间']}")
            
            if dry_run:
                print(f"\n⚠️  这是预览模式，没有实际执行迁移")
                print(f"如要执行实际迁移，请调用 execute_incremental_migration(dry_run=False)")
                return True
            
            # 确认执行
            print(f"\n⚠️  即将执行实际迁移操作...")
            
            # 构建迁移SQL
            field_list = ", ".join([f"`{field}`" for field in self.common_fields])
            
            migration_sql = f"""
            INSERT INTO `卡密系统` ({field_list})
            SELECT {field_list}
            FROM `kami3162`.`卡密系统` s
            WHERE s.`卡密` NOT IN (
                SELECT `卡密` FROM `卡密系统`
            )
            """
            
            # 执行迁移
            start_time = time.time()
            with self.target_conn.cursor() as cursor:
                cursor.execute(migration_sql)
                affected_rows = cursor.rowcount
                self.target_conn.commit()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n✅ 迁移完成!")
            print(f"   实际迁移记录数: {affected_rows}")
            print(f"   耗时: {duration:.2f} 秒")
            
            # 验证迁移结果
            self.verify_migration()
            
            return True
            
        except Exception as e:
            logger.error(f"迁移执行失败: {e}")
            if self.target_conn:
                self.target_conn.rollback()
            return False
    
    def verify_migration(self):
        """验证迁移结果"""
        try:
            with self.source_conn.cursor() as source_cursor:
                with self.target_conn.cursor() as target_cursor:
                    # 获取两个数据库的记录数
                    source_cursor.execute("SELECT COUNT(*) FROM `卡密系统`")
                    source_count = source_cursor.fetchone()[0]
                    
                    target_cursor.execute("SELECT COUNT(*) FROM `卡密系统`")
                    target_count = target_cursor.fetchone()[0]
                    
                    print(f"\n📊 迁移后统计:")
                    print(f"   源数据库记录数: {source_count}")
                    print(f"   目标数据库记录数: {target_count}")
                    
                    # 检查是否还有未迁移的记录
                    check_sql = """
                    SELECT COUNT(*) 
                    FROM `kami3162`.`卡密系统` s
                    WHERE s.`卡密` NOT IN (
                        SELECT `卡密` FROM `卡密系统`
                    )
                    """
                    target_cursor.execute(check_sql)
                    remaining = target_cursor.fetchone()[0]
                    
                    if remaining == 0:
                        print(f"✅ 验证通过: 所有源数据库记录都已存在于目标数据库")
                    else:
                        print(f"⚠️  仍有 {remaining} 条记录未迁移")
                        
        except Exception as e:
            logger.error(f"验证失败: {e}")
    
    def backup_target_database(self, backup_table_name=None):
        """备份目标数据库表"""
        if not backup_table_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_table_name = f"卡密系统_备份_{timestamp}"
        
        try:
            with self.target_conn.cursor() as cursor:
                backup_sql = f"CREATE TABLE `{backup_table_name}` AS SELECT * FROM `卡密系统`"
                cursor.execute(backup_sql)
                self.target_conn.commit()
                
                # 获取备份记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{backup_table_name}`")
                backup_count = cursor.fetchone()[0]
                
                print(f"✅ 备份完成: 表 `{backup_table_name}`, 记录数: {backup_count}")
                return backup_table_name
                
        except Exception as e:
            logger.error(f"备份失败: {e}")
            return None
    
    def close_connections(self):
        """关闭数据库连接"""
        if self.source_conn:
            self.source_conn.close()
        if self.target_conn:
            self.target_conn.close()

def main():
    """主函数"""
    migrator = DatabaseMigrator(源数据库配置, 目标数据库配置)
    
    try:
        # 连接数据库
        migrator.connect_databases()
        
        print(f"🚀 数据库迁移工具")
        print(f"源数据库: {源数据库配置['host']}")
        print(f"目标数据库: {目标数据库配置['host']}")
        
        # 预览迁移
        print(f"\n1️⃣ 执行迁移预览...")
        migrator.execute_incremental_migration(dry_run=True)
        
        # 询问是否执行实际迁移
        print(f"\n" + "="*60)
        choice = input("是否执行实际迁移? (y/N): ").strip().lower()
        
        if choice in ['y', 'yes']:
            # 询问是否备份
            backup_choice = input("是否先备份目标数据库? (Y/n): ").strip().lower()
            
            if backup_choice not in ['n', 'no']:
                print(f"\n2️⃣ 备份目标数据库...")
                backup_name = migrator.backup_target_database()
                if not backup_name:
                    print("备份失败，取消迁移操作")
                    return
            
            print(f"\n3️⃣ 执行实际迁移...")
            success = migrator.execute_incremental_migration(dry_run=False)
            
            if success:
                print(f"\n🎉 迁移成功完成!")
            else:
                print(f"\n❌ 迁移失败!")
        else:
            print(f"\n取消迁移操作")
        
    except Exception as e:
        logger.error(f"执行过程中出现错误: {e}")
    finally:
        migrator.close_connections()

if __name__ == "__main__":
    main()

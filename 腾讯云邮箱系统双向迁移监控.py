#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 腾讯云邮箱系统双向迁移监控工具
# 部署在腾讯云服务器152.136.103.102上
# 
# 功能：
# 1. 监控本地邮箱系统表，超过12天的数据迁移到火山云服务器的邮箱免费表
# 2. 如果本地邮箱系统表数据不足3000条，从火山云服务器补充最新数据
# 3. 如果火山云服务器没有数据可补充，则1分钟检查一次，否则每小时检查一次
#
# 使用方法：
#   前台运行:     python3 /腾讯云邮箱系统双向迁移监控.py
#   守护进程:     python3 /腾讯云邮箱系统双向迁移监控.py --daemon
#   查看状态:     python3 /腾讯云邮箱系统双向迁移监控.py --status
#   停止监控:     python3 /腾讯云邮箱系统双向迁移监控.py --stop
#   测试连接:     python3 /腾讯云邮箱系统双向迁移监控.py --test

import mysql.connector
from mysql.connector import Error
import time
import datetime
import logging
import schedule
import threading
from typing import Optional, Tuple, List
import signal
import sys
import os
import argparse
import atexit

# 腾讯云本地数据库配置
本地数据库配置 = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'YU6709',
    'database': 'kami3162',
    'charset': 'utf8mb4',
    'autocommit': False
}

# 火山云远程数据库配置
远程数据库配置 = {
    'host': '**************',
    'port': 3306,
    'user': 'root',
    'password': 'Yuyu6709.',
    'database': 'kami3162',
    'charset': 'utf8mb4',
    'autocommit': False
}

# 监控配置
监控配置 = {
    '最大天数': 13,
    '正常检查间隔_小时': 1,
    '无数据检查间隔_分钟': 1,
    '最小数据量': 1000,
    '批处理大小': 1000,
    '日志级别': 'INFO',
    '日志文件': '/腾讯云邮箱双向迁移监控.log',
    'PID文件': '/var/run/腾讯云邮箱监控.pid'
}

# 表配置
表配置 = {
    '本地源表名': '邮箱系统',
    '远程目标表名': '邮箱免费',
    '远程源表名': '邮箱系统',
    '本地目标表名': '邮箱系统',
    '创建时间字段候选': ['创建时间', 'created_at', 'create_time', 'created_time', 'add_time', 'insert_time']
}

# 安全配置
安全配置 = {
    '安全模式': False,
    '单次最大迁移数量': 10000,
    '迁移前备份': False
}

class 腾讯云双向迁移监控器:
    def __init__(self, daemon_mode=False):
        """初始化监控器"""
        self.daemon_mode = daemon_mode
        self.pid_file = 监控配置.get('PID文件', '/var/run/腾讯云邮箱监控.pid')
        
        # 设置日志
        self._设置日志()

        # 运行状态
        self.运行中 = False
        self.监控线程 = None

        # 统计信息
        self.总迁移数量_过期 = 0
        self.总迁移数量_补充 = 0
        self.最后检查时间 = None
        self.当前检查模式 = '正常模式'  # '正常模式' 或 '快速模式'
        
        # 设置信号处理
        self._设置信号处理()

    def _设置日志(self):
        """设置日志配置"""
        日志级别 = getattr(logging, 监控配置['日志级别'].upper(), logging.INFO)
        日志文件 = 监控配置['日志文件']
        
        handlers = [logging.FileHandler(日志文件, encoding='utf-8')]
        
        # 非守护进程模式下也输出到控制台
        if not self.daemon_mode:
            handlers.append(logging.StreamHandler())
        
        logging.basicConfig(
            level=日志级别,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=handlers
        )
        self.logger = logging.getLogger(__name__)
        
    def _设置信号处理(self):
        """设置信号处理"""
        signal.signal(signal.SIGTERM, self._信号处理器)
        signal.signal(signal.SIGINT, self._信号处理器)
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, self._信号处理器)
    
    def _信号处理器(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备停止监控...")
        self.停止监控()
        sys.exit(0)
    
    def _创建PID文件(self):
        """创建PID文件"""
        try:
            pid_dir = os.path.dirname(self.pid_file)
            if not os.path.exists(pid_dir):
                try:
                    os.makedirs(pid_dir, exist_ok=True)
                except PermissionError:
                    self.pid_file = './腾讯云邮箱监控.pid'
            
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
            
            atexit.register(self._删除PID文件)
            self.logger.info(f"PID文件已创建: {self.pid_file}")
        except Exception as e:
            self.logger.warning(f"创建PID文件失败: {e}")
    
    def _删除PID文件(self):
        """删除PID文件"""
        try:
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
                self.logger.info(f"PID文件已删除: {self.pid_file}")
        except Exception as e:
            self.logger.warning(f"删除PID文件失败: {e}")
    
    def _检查是否已运行(self):
        """检查是否已有实例在运行"""
        if not os.path.exists(self.pid_file):
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            try:
                os.kill(pid, 0)
                return True
            except OSError:
                os.remove(self.pid_file)
                return False
        except (ValueError, FileNotFoundError):
            return False
        
    def 获取数据库连接(self, 配置: dict) -> Optional[mysql.connector.MySQLConnection]:
        """获取数据库连接"""
        try:
            连接 = mysql.connector.connect(**配置)
            if 连接.is_connected():
                return 连接
        except Error as e:
            self.logger.error(f"数据库连接失败 {配置['host']}: {e}")
        return None
        
    def 检查表结构(self, 连接: mysql.connector.MySQLConnection, 表名: str) -> Tuple[bool, List[str], str]:
        """检查表结构并获取字段信息"""
        try:
            游标 = 连接.cursor()
            游标.execute(f"DESCRIBE `{表名}`")
            字段信息 = 游标.fetchall()
            字段名列表 = [字段[0] for 字段 in 字段信息]

            # 查找创建时间字段
            创建时间字段 = None
            for 字段 in 表配置['创建时间字段候选']:
                if 字段 in 字段名列表:
                    创建时间字段 = 字段
                    break

            if not 创建时间字段:
                for 字段 in 字段名列表:
                    if 'time' in 字段.lower() or 'date' in 字段.lower():
                        创建时间字段 = 字段
                        break

            游标.close()

            if not 创建时间字段:
                self.logger.error(f"未找到创建时间字段，请检查'{表名}'表结构")
                return False, [], ""

            return True, 字段名列表, 创建时间字段

        except Error as e:
            self.logger.error(f"检查表结构失败: {e}")
            return False, [], ""

    def 查找过期数据(self, 连接: mysql.connector.MySQLConnection, 表名: str, 创建时间字段: str) -> List[tuple]:
        """查找创建时间超过指定天数的数据"""
        try:
            游标 = 连接.cursor()
            截止时间 = datetime.datetime.now() - datetime.timedelta(days=监控配置['最大天数'])

            查询语句 = f"""
            SELECT * FROM `{表名}`
            WHERE `{创建时间字段}` < %s
            ORDER BY `{创建时间字段}` ASC
            LIMIT %s
            """

            游标.execute(查询语句, (截止时间, 监控配置['批处理大小']))
            过期数据 = 游标.fetchall()
            游标.close()
            return 过期数据

        except Error as e:
            self.logger.error(f"查找过期数据失败: {e}")
            return []

    def 查找最新数据(self, 连接: mysql.connector.MySQLConnection, 表名: str, 创建时间字段: str, 需要数量: int) -> List[tuple]:
        """查找最新的数据用于补充"""
        try:
            游标 = 连接.cursor()

            查询语句 = f"""
            SELECT * FROM `{表名}`
            ORDER BY `{创建时间字段}` DESC
            LIMIT %s
            """

            游标.execute(查询语句, (需要数量,))
            最新数据 = 游标.fetchall()
            游标.close()
            return 最新数据

        except Error as e:
            self.logger.error(f"查找最新数据失败: {e}")
            return []

    def 跨数据库迁移数据(self, 源连接: mysql.connector.MySQLConnection, 目标连接: mysql.connector.MySQLConnection,
                      数据批次: List[tuple], 字段名列表: List[str], 源表名: str, 目标表名: str) -> bool:
        """跨数据库迁移数据"""
        try:
            源游标 = 源连接.cursor()
            目标游标 = 目标连接.cursor()

            # 构建插入语句
            字段字符串 = ", ".join([f"`{字段}`" for 字段 in 字段名列表])
            占位符 = ", ".join(["%s"] * len(字段名列表))
            插入语句 = f"INSERT INTO `{目标表名}` ({字段字符串}) VALUES ({占位符})"

            # 插入数据到目标表
            目标游标.executemany(插入语句, 数据批次)
            插入数量 = 目标游标.rowcount

            # 删除源表中的数据
            if 字段名列表:
                ID字段 = 字段名列表[0]
                ID列表 = [行[0] for 行 in 数据批次]
                删除占位符 = ", ".join(["%s"] * len(ID列表))
                删除语句 = f"DELETE FROM `{源表名}` WHERE `{ID字段}` IN ({删除占位符})"
                源游标.execute(删除语句, ID列表)
                删除数量 = 源游标.rowcount

            # 提交事务
            源连接.commit()
            目标连接.commit()

            源游标.close()
            目标游标.close()

            self.logger.debug(f"跨数据库迁移成功：插入 {插入数量} 条，删除 {删除数量} 条")
            return True

        except Error as e:
            self.logger.error(f"跨数据库迁移失败: {e}")
            try:
                源连接.rollback()
                目标连接.rollback()
            except:
                pass
            return False

    def 获取表数据量(self, 连接: mysql.connector.MySQLConnection, 表名: str) -> int:
        """获取表的数据量"""
        try:
            游标 = 连接.cursor()
            游标.execute(f"SELECT COUNT(*) FROM `{表名}`")
            数量 = 游标.fetchone()[0]
            游标.close()
            return 数量
        except Error as e:
            self.logger.error(f"获取表数据量失败: {e}")
            return 0

    def 执行双向迁移检查(self):
        """执行双向迁移检查"""
        检查开始时间 = datetime.datetime.now()
        self.logger.info(f"开始执行双向迁移检查... (模式: {self.当前检查模式})")

        # 连接本地和远程数据库
        本地连接 = self.获取数据库连接(本地数据库配置)
        远程连接 = self.获取数据库连接(远程数据库配置)

        if not 本地连接 or not 远程连接:
            self.logger.error("数据库连接失败，跳过本次检查")
            return

        try:
            # 检查表结构
            本地结构正确, 本地字段列表, 本地时间字段 = self.检查表结构(本地连接, 表配置['本地源表名'])
            远程结构正确, 远程字段列表, 远程时间字段 = self.检查表结构(远程连接, 表配置['远程源表名'])

            if not 本地结构正确 or not 远程结构正确:
                return

            # 获取当前数据状态
            本地数据量 = self.获取表数据量(本地连接, 表配置['本地源表名'])
            远程数据量 = self.获取表数据量(远程连接, 表配置['远程源表名'])

            self.logger.info(f"当前数据状态 - 本地邮箱系统: {本地数据量:,} 条, 远程邮箱系统: {远程数据量:,} 条")

            # 第一步：处理本地过期数据（迁移到远程邮箱免费表）
            过期迁移数量 = self._处理过期数据迁移(本地连接, 远程连接, 本地字段列表, 本地时间字段)

            # 第二步：检查是否需要补充数据
            补充迁移数量 = self._处理数据补充(本地连接, 远程连接, 远程字段列表, 远程时间字段)

            # 更新检查模式
            self._更新检查模式(远程数据量, 补充迁移数量)

            检查耗时 = datetime.datetime.now() - 检查开始时间
            self.最后检查时间 = datetime.datetime.now()

            self.logger.info(f"双向迁移检查完成 - 过期迁移: {过期迁移数量} 条, 补充迁移: {补充迁移数量} 条, 耗时: {检查耗时}")

        finally:
            if 本地连接 and 本地连接.is_connected():
                本地连接.close()
            if 远程连接 and 远程连接.is_connected():
                远程连接.close()

    def _处理过期数据迁移(self, 本地连接, 远程连接, 字段列表, 时间字段) -> int:
        """处理过期数据迁移到远程邮箱免费表"""
        总迁移数量 = 0

        while True:
            过期数据 = self.查找过期数据(本地连接, 表配置['本地源表名'], 时间字段)

            if not 过期数据:
                break

            if self.跨数据库迁移数据(本地连接, 远程连接, 过期数据, 字段列表,
                                表配置['本地源表名'], 表配置['远程目标表名']):
                总迁移数量 += len(过期数据)
                self.总迁移数量_过期 += len(过期数据)
                self.logger.info(f"迁移过期数据 {len(过期数据)} 条到远程邮箱免费表")
            else:
                self.logger.error("过期数据迁移失败，停止处理")
                break

            if len(过期数据) < 监控配置['批处理大小']:
                break

        return 总迁移数量

    def _处理数据补充(self, 本地连接, 远程连接, 字段列表, 时间字段) -> int:
        """处理数据补充逻辑"""
        本地数据量 = self.获取表数据量(本地连接, 表配置['本地源表名'])
        最小数据量 = 监控配置['最小数据量']

        if 本地数据量 >= 最小数据量:
            self.logger.debug(f"本地数据量充足 ({本地数据量:,} >= {最小数据量:,})，无需补充")
            return 0

        需要补充数量 = 最小数据量 - 本地数据量
        self.logger.info(f"本地数据量不足 ({本地数据量:,} < {最小数据量:,})，需要补充 {需要补充数量:,} 条")

        # 从远程获取最新数据
        最新数据 = self.查找最新数据(远程连接, 表配置['远程源表名'], 时间字段, 需要补充数量)

        if not 最新数据:
            self.logger.warning("远程服务器没有数据可以补充")
            return 0

        # 分批迁移数据
        总补充数量 = 0
        批处理大小 = 监控配置['批处理大小']

        for i in range(0, len(最新数据), 批处理大小):
            批次数据 = 最新数据[i:i + 批处理大小]

            if self.跨数据库迁移数据(远程连接, 本地连接, 批次数据, 字段列表,
                                表配置['远程源表名'], 表配置['本地目标表名']):
                总补充数量 += len(批次数据)
                self.总迁移数量_补充 += len(批次数据)
                self.logger.info(f"从远程补充数据 {len(批次数据)} 条到本地邮箱系统表")
            else:
                self.logger.error("数据补充失败，停止处理")
                break

        return 总补充数量

    def _更新检查模式(self, 远程数据量: int, 补充迁移数量: int):
        """更新检查模式"""
        if 远程数据量 == 0 or 补充迁移数量 == 0:
            if self.当前检查模式 != '快速模式':
                self.当前检查模式 = '快速模式'
                self.logger.info("切换到快速检查模式（1分钟间隔）")
        else:
            if self.当前检查模式 != '正常模式':
                self.当前检查模式 = '正常模式'
                self.logger.info("切换到正常检查模式（1小时间隔）")

    def 启动监控(self):
        """启动持续监控"""
        if self.运行中:
            self.logger.warning("监控已在运行中")
            return

        if self._检查是否已运行():
            self.logger.error("检测到已有监控实例在运行，退出")
            return False

        self._创建PID文件()
        self.运行中 = True
        self.logger.info("启动腾讯云双向迁移监控")

        # 立即执行一次检查
        self.执行双向迁移检查()

        # 启动监控线程
        self.监控线程 = threading.Thread(target=self._监控循环, daemon=True)
        self.监控线程.start()

        return True

    def _监控循环(self):
        """监控循环"""
        while self.运行中:
            try:
                if self.当前检查模式 == '快速模式':
                    # 快速模式：1分钟检查一次
                    time.sleep(60)
                else:
                    # 正常模式：1小时检查一次
                    time.sleep(3600)

                if self.运行中:
                    self.执行双向迁移检查()

            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

    def 停止监控(self):
        """停止监控"""
        if not self.运行中:
            return

        self.运行中 = False
        self.logger.info("正在停止监控...")

        if self.监控线程:
            self.监控线程.join(timeout=5)

        self._删除PID文件()
        self.logger.info("监控已停止")

    def 获取状态(self) -> dict:
        """获取监控状态"""
        return {
            '运行状态': '运行中' if self.运行中 else '已停止',
            '检查模式': self.当前检查模式,
            '最小数据量': f"{监控配置['最小数据量']:,} 条",
            '过期天数': f"{监控配置['最大天数']} 天",
            '批处理大小': 监控配置['批处理大小'],
            '过期迁移总数': self.总迁移数量_过期,
            '补充迁移总数': self.总迁移数量_补充,
            '最后检查时间': self.最后检查时间.strftime('%Y-%m-%d %H:%M:%S') if self.最后检查时间 else '未检查'
        }

def 守护进程化():
    """将进程转为守护进程"""
    try:
        pid = os.fork()
        if pid > 0:
            sys.exit(0)
    except OSError as e:
        print(f"第一次fork失败: {e}")
        sys.exit(1)

    os.chdir("/")
    os.setsid()
    os.umask(0)

    try:
        pid = os.fork()
        if pid > 0:
            sys.exit(0)
    except OSError as e:
        print(f"第二次fork失败: {e}")
        sys.exit(1)

    sys.stdout.flush()
    sys.stderr.flush()

    with open('/dev/null', 'r') as f:
        os.dup2(f.fileno(), sys.stdin.fileno())
    with open('/dev/null', 'w') as f:
        os.dup2(f.fileno(), sys.stdout.fileno())
        os.dup2(f.fileno(), sys.stderr.fileno())

def 解析命令行参数():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='腾讯云邮箱系统双向迁移监控工具')
    parser.add_argument('-d', '--daemon', action='store_true', help='以守护进程模式运行')
    parser.add_argument('-s', '--stop', action='store_true', help='停止运行中的监控进程')
    parser.add_argument('-r', '--restart', action='store_true', help='重启监控进程')
    parser.add_argument('--status', action='store_true', help='查看监控状态')
    parser.add_argument('--test', action='store_true', help='测试数据库连接')
    return parser.parse_args()

def 停止现有进程():
    """停止现有的监控进程"""
    pid_file = 监控配置.get('PID文件', '/var/run/腾讯云邮箱监控.pid')
    if not os.path.exists(pid_file):
        print("没有找到运行中的监控进程")
        return False

    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        os.kill(pid, signal.SIGTERM)

        for _ in range(10):
            try:
                os.kill(pid, 0)
                time.sleep(1)
            except OSError:
                break
        else:
            try:
                os.kill(pid, signal.SIGKILL)
            except OSError:
                pass

        print(f"监控进程 {pid} 已停止")
        return True

    except (ValueError, FileNotFoundError, OSError) as e:
        print(f"停止进程失败: {e}")
        return False

def 查看状态():
    """查看监控状态"""
    pid_file = 监控配置.get('PID文件', '/var/run/腾讯云邮箱监控.pid')
    if not os.path.exists(pid_file):
        print("监控进程未运行")
        return

    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        try:
            os.kill(pid, 0)
            print(f"监控进程正在运行 (PID: {pid})")

            日志文件 = 监控配置['日志文件']
            if os.path.exists(日志文件):
                stat = os.stat(日志文件)
                修改时间 = datetime.datetime.fromtimestamp(stat.st_mtime)
                print(f"日志文件: {日志文件}")
                print(f"最后修改: {修改时间.strftime('%Y-%m-%d %H:%M:%S')}")

        except OSError:
            print("PID文件存在但进程未运行")
            os.remove(pid_file)

    except (ValueError, FileNotFoundError) as e:
        print(f"读取状态失败: {e}")

def 测试数据库连接():
    """测试数据库连接"""
    print("测试数据库连接...")

    # 测试本地数据库
    print("\n1. 测试本地数据库 (腾讯云)...")
    try:
        本地连接 = mysql.connector.connect(**本地数据库配置)
        if 本地连接.is_connected():
            print("✅ 本地数据库连接成功")
            游标 = 本地连接.cursor()
            游标.execute("SELECT VERSION()")
            版本 = 游标.fetchone()[0]
            print(f"   MySQL版本: {版本}")

            for 表名 in [表配置['本地源表名']]:
                游标.execute(f"SHOW TABLES LIKE '{表名}'")
                if 游标.fetchone():
                    游标.execute(f"SELECT COUNT(*) FROM `{表名}`")
                    数量 = 游标.fetchone()[0]
                    print(f"   表 '{表名}': {数量:,} 条记录")
                else:
                    print(f"   ❌ 表 '{表名}' 不存在")

            游标.close()
            本地连接.close()
    except Error as e:
        print(f"❌ 本地数据库连接失败: {e}")

    # 测试远程数据库
    print("\n2. 测试远程数据库 (火山云)...")
    try:
        远程连接 = mysql.connector.connect(**远程数据库配置)
        if 远程连接.is_connected():
            print("✅ 远程数据库连接成功")
            游标 = 远程连接.cursor()
            游标.execute("SELECT VERSION()")
            版本 = 游标.fetchone()[0]
            print(f"   MySQL版本: {版本}")

            for 表名 in [表配置['远程源表名'], 表配置['远程目标表名']]:
                游标.execute(f"SHOW TABLES LIKE '{表名}'")
                if 游标.fetchone():
                    游标.execute(f"SELECT COUNT(*) FROM `{表名}`")
                    数量 = 游标.fetchone()[0]
                    print(f"   表 '{表名}': {数量:,} 条记录")
                else:
                    print(f"   ❌ 表 '{表名}' 不存在")

            游标.close()
            远程连接.close()
    except Error as e:
        print(f"❌ 远程数据库连接失败: {e}")

def main():
    """主函数"""
    args = 解析命令行参数()

    if args.stop:
        停止现有进程()
        return

    if args.status:
        查看状态()
        return

    if args.test:
        测试数据库连接()
        return

    if args.restart:
        print("重启监控进程...")
        停止现有进程()
        time.sleep(2)

    if args.daemon:
        print("启动守护进程模式...")
        守护进程化()

    监控器 = 腾讯云双向迁移监控器(daemon_mode=args.daemon)

    try:
        if not args.daemon:
            print("=" * 60)
            print("腾讯云邮箱系统双向迁移监控工具")
            print("=" * 60)

            状态 = 监控器.获取状态()
            print("\n📋 监控配置:")
            for 键, 值 in 状态.items():
                print(f"   {键}: {值}")

            print(f"\n🔍 数据库配置:")
            print(f"   本地数据库: {本地数据库配置['host']}")
            print(f"   远程数据库: {远程数据库配置['host']}")

        if 监控器.启动监控():
            if not args.daemon:
                print(f"\n✅ 监控已启动！")
                print(f"💡 按 Ctrl+C 停止监控")

            while 监控器.运行中:
                time.sleep(1)
        else:
            print("启动监控失败")
            sys.exit(1)

    except KeyboardInterrupt:
        if not args.daemon:
            print(f"\n\n⏹️  收到停止信号...")
        监控器.停止监控()
        if not args.daemon:
            print("👋 监控已停止，程序退出")

    except Exception as e:
        监控器.logger.error(f"程序异常: {e}")
        监控器.停止监控()
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱表数据查看GUI界面
显示邮箱系统和邮箱免费表的最新100条数据
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import mysql.connector
from mysql.connector import Error
import threading
from datetime import datetime

class EmailTablesGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("邮箱表数据查看器")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f0f0')
        
        # 数据库配置
        self.db_config = {
            'host': '**************',
            'port': 3306,
            'user': 'root',
            'password': 'Yuyu6709.',
            'database': 'kami3162',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """设置用户界面"""
        
        # 标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="邮箱表数据查看器", 
                              font=('Arial', 18, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # 统计信息框架
        stats_frame = tk.Frame(self.root, bg='#ecf0f1', height=80)
        stats_frame.pack(fill='x', padx=5, pady=5)
        stats_frame.pack_propagate(False)
        
        # 左侧统计 - 邮箱系统
        left_stats_frame = tk.Frame(stats_frame, bg='#3498db', relief='raised', bd=2)
        left_stats_frame.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        
        self.system_count_label = tk.Label(left_stats_frame, text="邮箱系统: 加载中...", 
                                          font=('Arial', 14, 'bold'), 
                                          fg='white', bg='#3498db')
        self.system_count_label.pack(expand=True)
        
        # 右侧统计 - 邮箱免费
        right_stats_frame = tk.Frame(stats_frame, bg='#27ae60', relief='raised', bd=2)
        right_stats_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        self.free_count_label = tk.Label(right_stats_frame, text="邮箱免费: 加载中...", 
                                        font=('Arial', 14, 'bold'), 
                                        fg='white', bg='#27ae60')
        self.free_count_label.pack(expand=True)
        
        # 刷新按钮
        refresh_frame = tk.Frame(self.root, bg='#f0f0f0')
        refresh_frame.pack(fill='x', padx=5, pady=5)
        
        self.refresh_btn = tk.Button(refresh_frame, text="🔄 刷新数据", 
                                    font=('Arial', 12, 'bold'),
                                    bg='#e74c3c', fg='white',
                                    command=self.refresh_data,
                                    relief='raised', bd=3)
        self.refresh_btn.pack()
        
        # 主数据框架
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 左侧 - 邮箱系统表
        left_frame = tk.LabelFrame(main_frame, text="邮箱系统表 (最新100条)", 
                                  font=('Arial', 12, 'bold'),
                                  fg='#2c3e50', bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        # 邮箱系统表格
        self.system_tree = ttk.Treeview(left_frame, columns=('ID', '邮箱', '创建时间', '人数'), 
                                       show='headings', height=20)
        
        # 设置列标题和宽度
        self.system_tree.heading('ID', text='ID')
        self.system_tree.heading('邮箱', text='邮箱')
        self.system_tree.heading('创建时间', text='创建时间')
        self.system_tree.heading('人数', text='人数')
        
        self.system_tree.column('ID', width=80, anchor='center')
        self.system_tree.column('邮箱', width=200, anchor='w')
        self.system_tree.column('创建时间', width=150, anchor='center')
        self.system_tree.column('人数', width=80, anchor='center')
        
        # 滚动条
        system_scrollbar = ttk.Scrollbar(left_frame, orient='vertical', command=self.system_tree.yview)
        self.system_tree.configure(yscrollcommand=system_scrollbar.set)
        
        self.system_tree.pack(side='left', fill='both', expand=True)
        system_scrollbar.pack(side='right', fill='y')
        
        # 右侧 - 邮箱免费表
        right_frame = tk.LabelFrame(main_frame, text="邮箱免费表 (最新100条)", 
                                   font=('Arial', 12, 'bold'),
                                   fg='#2c3e50', bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True, padx=5)
        
        # 邮箱免费表格
        self.free_tree = ttk.Treeview(right_frame, columns=('ID', '邮箱', '创建时间', '人数'), 
                                     show='headings', height=20)
        
        # 设置列标题和宽度
        self.free_tree.heading('ID', text='ID')
        self.free_tree.heading('邮箱', text='邮箱')
        self.free_tree.heading('创建时间', text='创建时间')
        self.free_tree.heading('人数', text='人数')
        
        self.free_tree.column('ID', width=80, anchor='center')
        self.free_tree.column('邮箱', width=200, anchor='w')
        self.free_tree.column('创建时间', width=150, anchor='center')
        self.free_tree.column('人数', width=80, anchor='center')
        
        # 滚动条
        free_scrollbar = ttk.Scrollbar(right_frame, orient='vertical', command=self.free_tree.yview)
        self.free_tree.configure(yscrollcommand=free_scrollbar.set)
        
        self.free_tree.pack(side='left', fill='both', expand=True)
        free_scrollbar.pack(side='right', fill='y')
        
        # 状态栏
        self.status_bar = tk.Label(self.root, text="准备就绪", 
                                  relief='sunken', anchor='w',
                                  bg='#bdc3c7', fg='#2c3e50')
        self.status_bar.pack(side='bottom', fill='x')
    
    def connect_database(self):
        """连接数据库"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            return connection
        except Error as e:
            messagebox.showerror("数据库连接错误", f"无法连接到数据库:\n{e}")
            return None
    
    def load_data(self):
        """加载数据"""
        self.status_bar.config(text="正在加载数据...")
        self.refresh_btn.config(state='disabled', text="加载中...")
        
        # 使用线程避免界面冻结
        thread = threading.Thread(target=self._load_data_thread)
        thread.daemon = True
        thread.start()
    
    def _load_data_thread(self):
        """在线程中加载数据"""
        connection = self.connect_database()
        if not connection:
            self.root.after(0, lambda: self.refresh_btn.config(state='normal', text="🔄 刷新数据"))
            return
        
        try:
            cursor = connection.cursor()
            
            # 获取邮箱系统表数据
            cursor.execute("""
                SELECT id, 邮箱, 创建时间, 人数 
                FROM `邮箱系统` 
                ORDER BY 创建时间 DESC 
                LIMIT 100
            """)
            system_data = cursor.fetchall()
            
            # 获取邮箱系统表总数
            cursor.execute("SELECT COUNT(*) FROM `邮箱系统`")
            system_count = cursor.fetchone()[0]
            
            # 获取邮箱免费表数据
            cursor.execute("""
                SELECT id, 邮箱, 创建时间, 人数 
                FROM `邮箱免费` 
                ORDER BY 创建时间 DESC 
                LIMIT 100
            """)
            free_data = cursor.fetchall()
            
            # 获取邮箱免费表总数
            cursor.execute("SELECT COUNT(*) FROM `邮箱免费`")
            free_count = cursor.fetchone()[0]
            
            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_ui(system_data, system_count, free_data, free_count))
            
        except Error as e:
            self.root.after(0, lambda: messagebox.showerror("数据库错误", f"查询数据时出错:\n{e}"))
        finally:
            if connection and connection.is_connected():
                connection.close()
            self.root.after(0, lambda: self.refresh_btn.config(state='normal', text="🔄 刷新数据"))
    
    def _update_ui(self, system_data, system_count, free_data, free_count):
        """更新UI界面"""
        
        # 更新统计信息
        self.system_count_label.config(text=f"邮箱系统: {system_count:,} 条")
        self.free_count_label.config(text=f"邮箱免费: {free_count:,} 条")
        
        # 清空现有数据
        for item in self.system_tree.get_children():
            self.system_tree.delete(item)
        
        for item in self.free_tree.get_children():
            self.free_tree.delete(item)
        
        # 插入邮箱系统数据
        for row in system_data:
            # 格式化创建时间
            create_time = row[2].strftime('%Y-%m-%d %H:%M:%S') if row[2] else 'N/A'
            self.system_tree.insert('', 'end', values=(row[0], row[1], create_time, row[3]))
        
        # 插入邮箱免费数据
        for row in free_data:
            # 格式化创建时间
            create_time = row[2].strftime('%Y-%m-%d %H:%M:%S') if row[2] else 'N/A'
            self.free_tree.insert('', 'end', values=(row[0], row[1], create_time, row[3]))
        
        # 更新状态栏
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.status_bar.config(text=f"数据加载完成 - 更新时间: {current_time}")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()

def main():
    """主函数"""
    root = tk.Tk()
    app = EmailTablesGUI(root)
    
    # 设置窗口图标和其他属性
    try:
        root.state('zoomed')  # Windows下最大化
    except:
        root.attributes('-zoomed', True)  # Linux下最大化
    
    root.mainloop()

if __name__ == "__main__":
    main()

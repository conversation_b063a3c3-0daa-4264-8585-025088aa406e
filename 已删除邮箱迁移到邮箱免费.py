#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已删除邮箱迁移工具 - 将"已删除邮箱"表迁移17000个邮箱到"邮箱免费"表
从"创建时间"列最新的开始迁移，处理表结构不一致问题
"""

import mysql.connector
from mysql.connector import Error
import time

def 分析表结构():
    """分析已删除邮箱表和邮箱免费表的结构差异"""
    
    # 数据库配置
    数据库配置 = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': False
    }
    
    数据库连接 = None
    
    try:
        print("正在连接数据库...")
        数据库连接 = mysql.connector.connect(**数据库配置)
        
        if 数据库连接.is_connected():
            print("✅ 数据库连接成功!")
            
            游标 = 数据库连接.cursor()
            
            print("\n" + "="*80)
            print("表结构分析工具")
            print("="*80)
            
            # 1. 分析已删除邮箱表结构
            print("\n1. 📋 已删除邮箱表结构:")
            游标.execute("DESCRIBE `已删除邮箱`")
            已删除邮箱字段 = 游标.fetchall()
            
            print(f"   字段数量: {len(已删除邮箱字段)} 个")
            for 字段 in 已删除邮箱字段:
                字段名, 类型, 是否为空, 键, 默认值, 额外 = 字段
                print(f"   - {字段名}: {类型} {'NOT NULL' if 是否为空 == 'NO' else 'NULL'} {键 if 键 else ''}")
            
            # 2. 分析邮箱免费表结构
            print("\n2. 📋 邮箱免费表结构:")
            游标.execute("DESCRIBE `邮箱免费`")
            邮箱免费字段 = 游标.fetchall()
            
            print(f"   字段数量: {len(邮箱免费字段)} 个")
            for 字段 in 邮箱免费字段:
                字段名, 类型, 是否为空, 键, 默认值, 额外 = 字段
                print(f"   - {字段名}: {类型} {'NOT NULL' if 是否为空 == 'NO' else 'NULL'} {键 if 键 else ''}")
            
            # 3. 对比字段差异
            print("\n3. 🔍 字段对比分析:")
            已删除字段名 = [字段[0] for 字段 in 已删除邮箱字段]
            免费字段名 = [字段[0] for 字段 in 邮箱免费字段]
            
            共同字段 = set(已删除字段名) & set(免费字段名)
            已删除独有 = set(已删除字段名) - set(免费字段名)
            免费独有 = set(免费字段名) - set(已删除字段名)
            
            print(f"   共同字段 ({len(共同字段)} 个): {', '.join(sorted(共同字段))}")
            print(f"   已删除邮箱独有 ({len(已删除独有)} 个): {', '.join(sorted(已删除独有))}")
            print(f"   邮箱免费独有 ({len(免费独有)} 个): {', '.join(sorted(免费字段名))}")
            
            # 4. 检查数据量
            print("\n4. 📊 数据量统计:")
            游标.execute("SELECT COUNT(*) FROM `已删除邮箱`")
            已删除总量 = 游标.fetchone()[0]
            
            游标.execute("SELECT COUNT(*) FROM `邮箱免费`")
            免费总量 = 游标.fetchone()[0]
            
            print(f"   已删除邮箱表: {已删除总量:,} 条")
            print(f"   邮箱免费表: {免费总量:,} 条")
            
            # 5. 检查创建时间字段
            print("\n5. ⏰ 创建时间分析:")
            if '创建时间' in 已删除字段名:
                游标.execute("SELECT MIN(创建时间), MAX(创建时间) FROM `已删除邮箱` WHERE 创建时间 IS NOT NULL")
                结果 = 游标.fetchone()
                if 结果[0] and 结果[1]:
                    print(f"   已删除邮箱 - 最早: {结果[0]}")
                    print(f"   已删除邮箱 - 最晚: {结果[1]}")
                
                # 查看最新的几条记录
                游标.execute("SELECT 邮箱, 创建时间, 删除原因 FROM `已删除邮箱` ORDER BY 创建时间 DESC LIMIT 5")
                最新记录 = 游标.fetchall()
                print(f"   最新5条记录:")
                for i, 记录 in enumerate(最新记录, 1):
                    print(f"     {i}. {记录[0]} | {记录[1]} | {记录[2] if 记录[2] else '无原因'}")
            
            游标.close()
            
            return {
                '已删除字段': 已删除字段名,
                '免费字段': 免费字段名,
                '共同字段': list(共同字段),
                '已删除总量': 已删除总量,
                '免费总量': 免费总量
            }
            
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return None
        
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return None
        
    finally:
        if 数据库连接 and 数据库连接.is_connected():
            数据库连接.close()
            print("\n数据库连接已关闭")

def 已删除邮箱迁移():
    """将已删除邮箱表中的17000条数据迁移到邮箱免费表"""
    
    # 先分析表结构
    print("开始分析表结构...")
    结构信息 = 分析表结构()
    
    if not 结构信息:
        print("❌ 表结构分析失败，无法继续迁移")
        return False
    
    # 数据库配置
    数据库配置 = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': False
    }
    
    # 迁移数量
    迁移数量 = 17000
    
    数据库连接 = None
    
    try:
        print(f"\n{'='*80}")
        print("已删除邮箱迁移工具")
        print(f"从 '已删除邮箱' 迁移最新 {迁移数量:,} 条数据到 '邮箱免费'")
        print("="*80)
        
        print("\n正在重新连接数据库...")
        数据库连接 = mysql.connector.connect(**数据库配置)
        
        if 数据库连接.is_connected():
            print("✅ 数据库连接成功!")
            
            游标 = 数据库连接.cursor()
            
            # 检查数据量
            if 结构信息['已删除总量'] < 迁移数量:
                实际迁移数量 = 结构信息['已删除总量']
                print(f"⚠️  已删除邮箱表只有 {实际迁移数量:,} 条数据，将全部迁移")
            else:
                实际迁移数量 = 迁移数量
                print(f"📋 计划迁移 {实际迁移数量:,} 条最新数据")
            
            # 根据表结构差异制定迁移策略
            共同字段 = 结构信息['共同字段']
            
            if not 共同字段:
                print("❌ 两个表没有共同字段，无法迁移")
                return False
            
            print(f"\n📋 迁移策略:")
            print(f"   共同字段: {', '.join(共同字段)}")
            print(f"   迁移方式: 只迁移共同字段")
            print(f"   排序方式: 按创建时间降序（最新的优先）")
            
            # 确认迁移
            print(f"\n⚠️  即将迁移 {实际迁移数量:,} 条数据")
            print(f"   数据库: {数据库配置['host']}")
            print(f"   源表: 已删除邮箱")
            print(f"   目标表: 邮箱免费")
            
            确认 = input("\n确认执行迁移? (输入 'YES' 确认): ").strip()
            
            if 确认 != 'YES':
                print("❌ 迁移已取消")
                return False
            
            # 开始迁移
            print(f"\n🚀 开始执行数据迁移...")
            开始时间 = time.time()
            
            # 构建SQL语句
            共同字段字符串 = ", ".join([f"`{字段}`" for 字段 in 共同字段])
            占位符 = ", ".join(["%s"] * len(共同字段))
            
            # 分批处理，每批1000条
            批处理大小 = 1000
            总迁移数量 = 0
            
            print(f"   使用批处理模式，每批 {批处理大小} 条")
            
            for 偏移量 in range(0, 实际迁移数量, 批处理大小):
                当前批次大小 = min(批处理大小, 实际迁移数量 - 偏移量)
                
                print(f"\n   处理批次 {偏移量//批处理大小 + 1}: {偏移量 + 1} - {偏移量 + 当前批次大小}")
                
                # 获取一批数据（按创建时间降序）
                批次开始时间 = time.time()
                查询SQL = f"""
                    SELECT {共同字段字符串} 
                    FROM `已删除邮箱` 
                    ORDER BY 创建时间 DESC 
                    LIMIT %s OFFSET %s
                """
                游标.execute(查询SQL, (当前批次大小, 偏移量))
                批次数据 = 游标.fetchall()
                
                if not 批次数据:
                    print("   ⚠️  没有更多数据")
                    break
                
                # 插入到邮箱免费表
                插入查询 = f"INSERT INTO `邮箱免费` ({共同字段字符串}) VALUES ({占位符})"
                游标.executemany(插入查询, 批次数据)
                
                总迁移数量 += len(批次数据)
                批次耗时 = time.time() - 批次开始时间
                
                print(f"   ✅ 批次完成: {len(批次数据)} 条 (耗时: {批次耗时:.3f}秒)")
                
                # 显示进度
                进度 = (总迁移数量 / 实际迁移数量) * 100
                print(f"   📈 总进度: {总迁移数量:,}/{实际迁移数量:,} ({进度:.1f}%)")
            
            # 提交事务
            print("\n   步骤: 提交事务...")
            数据库连接.commit()
            print("   ✅ 事务提交成功")
            
            # 最终验证
            print(f"\n📋 最终验证...")
            
            游标.execute("SELECT COUNT(*) FROM `已删除邮箱`")
            源数据量_迁移后 = 游标.fetchone()[0]
            
            游标.execute("SELECT COUNT(*) FROM `邮箱免费`")
            目标数据量_迁移后 = 游标.fetchone()[0]
            
            总耗时 = time.time() - 开始时间
            
            print(f"\n" + "="*80)
            print("🎉 已删除邮箱迁移完成!")
            print("="*80)
            
            print(f"📊 迁移结果:")
            print(f"   已删除邮箱表: {结构信息['已删除总量']:,} 条 (未删除源数据)")
            print(f"   邮箱免费表: {结构信息['免费总量']:,} → {目标数据量_迁移后:,} 条")
            print(f"   实际迁移数量: {总迁移数量:,} 条")
            
            print(f"\n⏱️  性能统计:")
            print(f"   总耗时: {总耗时:.3f}秒")
            print(f"   平均速度: {总迁移数量/总耗时:.0f} 条/秒")
            
            print(f"\n✅ 迁移成功!")
            print(f"   ✅ {总迁移数量:,} 条最新数据已从 '已删除邮箱' 迁移到 '邮箱免费'")
            print(f"   ✅ 源表数据保持不变")
            
            游标.close()
            
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        if 数据库连接:
            try:
                数据库连接.rollback()
                print("🔄 事务已回滚")
            except:
                pass
        return False
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
        if 数据库连接:
            try:
                数据库连接.rollback()
                print("🔄 事务已回滚")
            except:
                pass
        return False
        
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        if 数据库连接:
            try:
                数据库连接.rollback()
                print("🔄 事务已回滚")
            except:
                pass
        return False
        
    finally:
        if 数据库连接 and 数据库连接.is_connected():
            数据库连接.close()
            print("\n数据库连接已关闭")
    
    return True

if __name__ == "__main__":
    print("=" * 80)
    print("已删除邮箱迁移工具")
    print("将'已删除邮箱'表中最新17000条数据迁移到'邮箱免费'表")
    print("=" * 80)
    
    成功 = 已删除邮箱迁移()
    
    if 成功:
        print("\n🎉 已删除邮箱迁移成功完成!")
    else:
        print("\n❌ 已删除邮箱迁移失败或被取消")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱数据迁移工具 - 从***************的邮箱备用表迁移所有数据到邮箱免费表
跨数据库数据迁移，从邮箱备用表移动到邮箱免费表
"""

import mysql.connector
from mysql.connector import Error
import time

def 邮箱数据迁移():
    """从***************的邮箱备用表迁移所有数据到目标数据库的邮箱免费表"""
    
    # 源数据库配置 (***************)
    源数据库配置 = {
        'host': '***************',
        'port': 3306,
        'user': 'root',
        'password': 'YU6709',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': False
    }
    
    # 目标数据库配置 (**************)
    目标数据库配置 = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': False
    }
    
    # 迁移所有数据（不限制数量）
    # 迁移数量 = 0  # 0表示迁移所有数据
    
    源数据库连接 = None
    目标数据库连接 = None
    
    try:
        print("正在连接源数据库 (***************)...")
        源数据库连接 = mysql.connector.connect(**源数据库配置)
        
        print("正在连接目标数据库 (**************)...")
        目标数据库连接 = mysql.connector.connect(**目标数据库配置)
        
        if 源数据库连接.is_connected() and 目标数据库连接.is_connected():
            print("✅ 数据库连接成功!")
            
            源游标 = 源数据库连接.cursor()
            目标游标 = 目标数据库连接.cursor()
            
            print("\n" + "="*60)
            print("邮箱数据迁移工具执行")
            print(f"从 '邮箱备用' 迁移到 '邮箱免费'")
            print(f"迁移模式: 全量迁移 (所有数据)")
            print("="*60)
            
            # 1. 获取数据状态
            print("1. 📊 检查数据状态...")
            源游标.execute("SELECT COUNT(*) FROM `邮箱备用`")
            源数据总量 = 源游标.fetchone()[0]
            
            目标游标.execute("SELECT COUNT(*) FROM `邮箱免费`")
            目标数据量_迁移前 = 目标游标.fetchone()[0]
            
            print(f"   源数据库 '邮箱备用': {源数据总量:,} 条")
            print(f"   目标数据库 '邮箱免费': {目标数据量_迁移前:,} 条")

            if 源数据总量 == 0:
                print("❌ 源数据库中没有数据需要迁移")
                return False

            # 迁移所有数据
            实际迁移数量 = 源数据总量

            print(f"\n2. 📋 迁移计划:")
            print(f"   迁移模式: 全量迁移")
            print(f"   迁移数量: {实际迁移数量:,} 条 (所有数据)")
            
            # 3. 获取表结构信息
            print("\n3. 🔍 获取表结构...")
            源游标.execute("DESCRIBE `邮箱备用`")
            字段信息 = 源游标.fetchall()
            
            # 构建列名列表
            字段名列表 = [字段[0] for 字段 in 字段信息]
            字段字符串 = ", ".join([f"`{字段}`" for 字段 in 字段名列表])
            占位符 = ", ".join(["%s"] * len(字段名列表))
            
            print(f"   表字段数: {len(字段名列表)} 个")
            print(f"   字段列表: {', '.join(字段名列表[:5])}{'...' if len(字段名列表) > 5 else ''}")
            
            # 4. 确认迁移
            print(f"\n⚠️  即将迁移 {实际迁移数量:,} 条数据 (全部数据)")
            print(f"   源数据库: {源数据库配置['host']} -> 邮箱备用表")
            print(f"   目标数据库: {目标数据库配置['host']} -> 邮箱免费表")
            
            确认 = input("\n确认执行迁移? (输入 'YES' 确认): ").strip()
            
            if 确认 != 'YES':
                print("❌ 迁移已取消")
                return False
            
            # 5. 开始迁移
            print(f"\n🚀 开始执行数据迁移...")
            开始时间 = time.time()
            
            # 分批处理，每批2000条
            批处理大小 = 2000
            总迁移数量 = 0
            已迁移ID列表 = []
            
            print(f"   使用批处理模式，每批 {批处理大小} 条")
            
            for 偏移量 in range(0, 实际迁移数量, 批处理大小):
                当前批次大小 = min(批处理大小, 实际迁移数量 - 偏移量)
                
                print(f"\n   处理批次 {偏移量//批处理大小 + 1}: {偏移量 + 1} - {偏移量 + 当前批次大小}")
                
                # 获取一批数据
                批次开始时间 = time.time()
                源游标.execute(f"SELECT {字段字符串} FROM `邮箱备用` LIMIT %s OFFSET %s",
                              (当前批次大小, 偏移量))
                批次数据 = 源游标.fetchall()
                
                if not 批次数据:
                    print("   ⚠️  没有更多数据")
                    break
                
                # 插入到目标数据库的邮箱免费表
                插入查询 = f"INSERT INTO `邮箱免费` ({字段字符串}) VALUES ({占位符})"
                目标游标.executemany(插入查询, 批次数据)
                目标数据库连接.commit()
                
                # 记录已迁移的数据ID（假设第一列是ID）
                if 字段名列表[0].lower() in ['id', 'email_id', 'mail_id']:
                    批次ID列表 = [行[0] for 行 in 批次数据]
                    已迁移ID列表.extend(批次ID列表)
                
                总迁移数量 += len(批次数据)
                批次耗时 = time.time() - 批次开始时间
                
                print(f"   ✅ 批次完成: {len(批次数据)} 条 (耗时: {批次耗时:.3f}秒)")
                
                # 显示进度
                进度 = (总迁移数量 / 实际迁移数量) * 100
                print(f"   📈 总进度: {总迁移数量:,}/{实际迁移数量:,} ({进度:.1f}%)")
                
                # 每10批显示一次速度统计
                if (偏移量 // 批处理大小 + 1) % 10 == 0:
                    当前耗时 = time.time() - 开始时间
                    当前速度 = 总迁移数量 / 当前耗时
                    print(f"   ⚡ 当前速度: {当前速度:.0f} 条/秒")
            
            # 6. 删除源数据库中已迁移的数据
            print(f"\n🗑️  删除源数据库中已迁移的数据...")
            删除开始时间 = time.time()
            
            if 已迁移ID列表 and 字段名列表[0].lower() in ['id', 'email_id', 'mail_id']:
                # 分批删除
                删除批处理大小 = 1000
                总删除数量 = 0
                
                for i in range(0, len(已迁移ID列表), 删除批处理大小):
                    批次ID = 已迁移ID列表[i:i + 删除批处理大小]
                    删除占位符 = ", ".join(["%s"] * len(批次ID))
                    
                    删除查询 = f"DELETE FROM `邮箱备用` WHERE `{字段名列表[0]}` IN ({删除占位符})"
                    源游标.execute(删除查询, 批次ID)
                    
                    总删除数量 += 源游标.rowcount
                    
                    if (i // 删除批处理大小 + 1) % 10 == 0:
                        print(f"   删除进度: {总删除数量:,}/{len(已迁移ID列表):,}")
                
                源数据库连接.commit()
                删除耗时 = time.time() - 删除开始时间
                print(f"   ✅ 删除完成: {总删除数量:,} 条 (耗时: {删除耗时:.3f}秒)")
            else:
                # 如果无法通过ID删除，则删除所有记录
                源游标.execute("DELETE FROM `邮箱备用`")
                源数据库连接.commit()
                删除耗时 = time.time() - 删除开始时间
                print(f"   ✅ 删除完成: {源游标.rowcount:,} 条 (耗时: {删除耗时:.3f}秒)")
            
            # 7. 最终验证
            print(f"\n📋 最终验证...")

            源游标.execute("SELECT COUNT(*) FROM `邮箱备用`")
            源数据量_迁移后 = 源游标.fetchone()[0]
            
            目标游标.execute("SELECT COUNT(*) FROM `邮箱免费`")
            目标数据量_迁移后 = 目标游标.fetchone()[0]
            
            总耗时 = time.time() - 开始时间
            
            print(f"\n" + "="*60)
            print("🎉 邮箱数据迁移完成!")
            print("="*60)
            
            print(f"📊 迁移结果:")
            print(f"   源数据库 '邮箱备用': {源数据总量:,} → {源数据量_迁移后:,} 条")
            print(f"   目标数据库 '邮箱免费': {目标数据量_迁移前:,} → {目标数据量_迁移后:,} 条")
            print(f"   实际迁移数量: {总迁移数量:,} 条")
            
            print(f"\n⏱️  性能统计:")
            print(f"   总耗时: {总耗时:.3f}秒")
            print(f"   平均速度: {总迁移数量/总耗时:.0f} 条/秒")
            
            # 验证数据完整性
            期望目标数量 = 目标数据量_迁移前 + 总迁移数量
            if 目标数据量_迁移后 == 期望目标数量:
                print(f"\n✅ 迁移成功!")
                print(f"   ✅ 数据完整性验证通过")
                print(f"   ✅ {总迁移数量:,} 条数据已成功从 '邮箱备用' 迁移到 '邮箱免费'")
                print(f"   ✅ 源表 '邮箱备用' 已清空")
            else:
                print(f"\n⚠️  数据验证异常")
                print(f"   期望目标数量: {期望目标数量:,}")
                print(f"   实际目标数量: {目标数据量_迁移后:,}")
            
            源游标.close()
            目标游标.close()
            
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        if 源数据库连接:
            try:
                源数据库连接.rollback()
            except:
                pass
        if 目标数据库连接:
            try:
                目标数据库连接.rollback()
            except:
                pass
        return False
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
        if 源数据库连接:
            try:
                源数据库连接.rollback()
                print("🔄 源数据库事务已回滚")
            except:
                pass
        if 目标数据库连接:
            try:
                目标数据库连接.rollback()
                print("🔄 目标数据库事务已回滚")
            except:
                pass
        return False
        
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        if 源数据库连接:
            try:
                源数据库连接.rollback()
            except:
                pass
        if 目标数据库连接:
            try:
                目标数据库连接.rollback()
            except:
                pass
        return False
        
    finally:
        if 源数据库连接 and 源数据库连接.is_connected():
            源数据库连接.close()
            print("\n源数据库连接已关闭")
        if 目标数据库连接 and 目标数据库连接.is_connected():
            目标数据库连接.close()
            print("目标数据库连接已关闭")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("邮箱数据迁移工具")
    print("从***************的'邮箱备用'迁移所有数据到'邮箱免费'")
    print("=" * 60)

    成功 = 邮箱数据迁移()

    if 成功:
        print("\n🎉 邮箱数据迁移成功完成!")
    else:
        print("\n❌ 邮箱数据迁移失败或被取消")


import os
import shutil
import sqlite3
import uuid
import json
import sys
import argparse
from pathlib import Path
from datetime import datetime

# --- Text Formatting and Logging (inspired by your bash scripts) ---
class Colors:
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BOLD = '\033[1m'
    RESET = '\033[0m'

def log_info(message):
    print(f"{Colors.BLUE}[INFO]{Colors.RESET} {message}")

def log_success(message):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.RESET} {message}")

def log_warning(message):
    print(f"{Colors.YELLOW}[WARNING]{Colors.RESET} {message}")

def log_error(message):
    print(f"{Colors.RED}[ERROR]{Colors.RESET} {message}")

# --- Helper Functions ---

def get_vscode_user_data_path():
    """
    Determines the VS Code user data path based on the operating system.
    Supports stable and Insider versions.
    """
    if sys.platform == "win32":
        appdata_path = Path(os.getenv('APPDATA'))
        user_data_paths = [
            appdata_path / 'Code' / 'User',
            appdata_path / 'Code - Insiders' / 'User'
        ]
    elif sys.platform == "darwin": # macOS
        user_data_paths = [
            Path.home() / 'Library' / 'Application Support' / 'Code' / 'User',
            Path.home() / 'Library' / 'Application Support' / 'Code - Insiders' / 'User'
        ]
    else: # Linux
        user_data_paths = [
            Path.home() / '.config' / 'Code' / 'User',
            Path.home() / '.config' / 'Code - Insiders' / 'User'
        ]

    for path in user_data_paths:
        if path.is_dir():
            return path
    return None

def backup_file(filepath: Path) -> bool:
    """
    Creates a timestamped backup of a file.
    """
    if not filepath.exists():
        log_warning(f"File not found, skipping backup: {filepath.name}")
        return False

    backup_dir = filepath.parent / "backups"
    backup_dir.mkdir(parents=True, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"{filepath.name}.{timestamp}.bak"
    backup_path = backup_dir / backup_filename

    try:
        shutil.copy2(str(filepath), str(backup_path))
        log_success(f"Backed up {filepath.name} to {backup_path}")
        return True
    except Exception as e:
        log_error(f"Failed to backup {filepath.name}: {e}")
        return False

# --- Telemetry ID Modification (from id_modifier.sh) ---

def generate_machine_id() -> str:
    """Generates a random 64-character hex string for machineId."""
    return os.urandom(32).hex()

def generate_device_id() -> str:
    """Generates a random UUID v4 for devDeviceId."""
    return str(uuid.uuid4())

def modify_telemetry_ids():
    """
    Modifies telemetry IDs in VS Code's storage.json file.
    """
    log_info("\n--- Modifying VS Code Telemetry IDs ---")

    vscode_user_data_path = get_vscode_user_data_path()
    if not vscode_user_data_path:
        log_error("Could not find VS Code user data directory. Exiting telemetry ID modification.")
        return

    # In VS Code, the `telemetry.machineId` and `telemetry.devDeviceId`
    # are often found in `storage.json` within the `globalStorage` directory.
    # Note: Your bash script looked for storage.json in globalStorage, which is correct.
    # My previous Python script looked for MachineId file, which might be an older location or for a different purpose.
    # We will stick to storage.json as per your bash script.
    storage_json_path = vscode_user_data_path / 'globalStorage' / 'storage.json'

    if not storage_json_path.exists():
        log_warning(f"'{storage_json_path.name}' not found. Skipping telemetry ID modification.")
        log_info("If this is a fresh VS Code install, the IDs might be generated on first run.")
        return

    if not backup_file(storage_json_path):
        log_error("Failed to backup storage.json. Aborting telemetry ID modification.")
        return

    try:
        with open(storage_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        new_machine_id = generate_machine_id()
        new_device_id = generate_device_id()

        data["telemetry.machineId"] = new_machine_id
        data["telemetry.devDeviceId"] = new_device_id

        with open(storage_json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4) # Use indent for readability

        log_success(f"Successfully updated telemetry IDs in '{storage_json_path.name}'.")
        log_info(f"New machineId: {new_machine_id}")
        log_info(f"New devDeviceId: {new_device_id}")
        log_info("Please restart VS Code for changes to take effect.")

    except json.JSONDecodeError as e:
        log_error(f"Error reading or parsing '{storage_json_path.name}' (invalid JSON): {e}")
    except Exception as e:
        log_error(f"An unexpected error occurred during telemetry ID modification: {e}")

# --- Database Cleaning (from clean_code_db.sh) ---

def clean_vscode_databases():
    """
    Removes "augment"-related entries from VS Code's SQLite databases.
    Specifically targeting 'state.vscdb' as per your bash script.
    """
    log_info("\n--- Cleaning VS Code Databases ---")

    vscode_user_data_path = get_vscode_user_data_path()
    if not vscode_user_data_path:
        log_error("Could not find VS Code user data directory. Exiting database cleaning.")
        return

    # Your bash script specifically targets 'state.vscdb'
    db_path = vscode_user_data_path / 'globalStorage' / 'state.vscdb'

    if not db_path.exists():
        log_warning(f"Database file '{db_path.name}' not found. Skipping database cleaning.")
        return

    if not backup_file(db_path):
        log_error(f"Failed to backup {db_path.name}. Aborting database cleaning.")
        return

    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # The SQL command from your bash script: DELETE FROM ItemTable WHERE key LIKE '%augment%';
        sql_query = "DELETE FROM ItemTable WHERE key LIKE '%augment%';"

        # Check how many rows would be deleted first (optional, for better logging)
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%';")
        rows_to_delete_count = cursor.fetchone()[0]

        if rows_to_delete_count > 0:
            log_warning(f"Found {rows_to_delete_count} potential 'Augment' related entries in '{db_path.name}'. Attempting to remove...")
            cursor.execute(sql_query)
            conn.commit()
            log_success(f"Successfully removed {rows_to_delete_count} entries from '{db_path.name}'.")
        else:
            log_info(f"No 'Augment' related entries found in '{db_path.name}'.")

        conn.close()

    except sqlite3.Error as e:
        log_error(f"Error accessing or cleaning '{db_path.name}': {e}")
    except Exception as e:
        log_error(f"An unexpected error occurred during database cleaning: {e}")

# --- Main Installation and Execution Logic (inspired by install.sh) ---

def main():
    parser = argparse.ArgumentParser(
        description="VS Code Augment VIP Maintenance Tool",
        formatter_class=argparse.RawTextHelpFormatter # For better help message formatting
    )
    parser.add_argument('--clean', action='store_true',
                        help='Run database cleaning script.')
    parser.add_argument('--modify-ids', action='store_true',
                        help='Run telemetry ID modification script.')
    parser.add_argument('--all', action='store_true',
                        help='Run all scripts (clean and modify IDs).')

    args = parser.parse_args()

    log_info(f"{Colors.BOLD}Starting VS Code Augment VIP Maintenance Tool{Colors.RESET}")
    log_info("Please ensure VS Code is completely closed before proceeding.")

    run_clean = args.clean or args.all
    run_modify_ids = args.modify_ids or args.all

    if not run_clean and not run_modify_ids:
        # If no arguments, prompt user interactively
        log_info("No specific actions requested via command-line arguments.")
        print() # New line for readability

        # Ask about database cleaning
        response = input(f"{Colors.YELLOW}Would you like to clean VS Code databases now? (y/N): {Colors.RESET}").strip().lower()
        if response == 'y':
            run_clean = True

        # Ask about telemetry ID modification
        response = input(f"{Colors.YELLOW}Would you like to modify VS Code telemetry IDs now? (y/N): {Colors.RESET}").strip().lower()
        if response == 'y':
            run_modify_ids = True

    if run_clean:
        clean_vscode_databases()

    if run_modify_ids:
        modify_telemetry_ids()

    if not run_clean and not run_modify_ids:
        log_info("No actions were performed.")
    else:
        log_success("All requested operations completed.")
        log_info("Remember to restart VS Code for changes to take full effect.")

    print(f"\n{Colors.BOLD}Exiting VS Code Augment VIP Maintenance Tool.{Colors.RESET}")

if __name__ == "__main__":
    # Initialize colorama for Windows if available (for CMD/PowerShell)
    if sys.platform == "win32":
        try:
            from colorama import init
            init(autoreset=True)
        except ImportError:
            log_warning("Colorama not installed. Output may not be color-coded on Windows. Install with: pip install colorama")

    main()
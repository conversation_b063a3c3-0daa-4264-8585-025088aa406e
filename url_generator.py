
import random

def generate_session_id():
    """生成26位的authorization_session_id"""
    # 使用观察到的字符集：数字和大写字母（排除容易混淆的字符）
    chars = "0123456789ABCDEFGHJKMNPQRSTVWXYZ"
    return ''.join(random.choice(chars) for _ in range(26))

def generate_cursor_url():
    """生成Cursor认证URL"""
    base_url = "https://authenticator.cursor.sh/sign-up"
    
    # 固定参数（从原始URL中提取）
    state = "%257B%2522returnTo%2522%253A%2522%252Fsettings%2522%257D"
    redirect_uri = "https%3A%2F%2Fcursor.com%2Fapi%2Fauth%2Fcallback"
    
    # 生成随机的session ID
    session_id = generate_session_id()
    
    # 组装完整URL
    url = f"{base_url}?state={state}&redirect_uri={redirect_uri}&authorization_session_id={session_id}"
    
    return url

def main():
    """主函数"""
    print("=== Cursor认证URL生成器 ===\n")
    
    # 生成并显示10个URL
    for i in range(1, 11):
        url = generate_cursor_url()
        print(f"{i:2d}. {url}")

if __name__ == "__main__":
    main()

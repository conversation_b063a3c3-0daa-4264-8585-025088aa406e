#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从邮箱系统表中提取10个账号，删除这些账号，并保存到桌面的Excel文档
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import os
from datetime import datetime

def extract_and_delete_accounts():
    """从邮箱系统表中提取10个账号，删除这些账号，并保存到桌面的Excel文档"""

    # 数据库配置，与query_table_counts.py中相同
    config = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': True
    }

    connection = None
    
    try:
        print("正在连接数据库...")
        connection = mysql.connector.connect(**config)

        if connection.is_connected():
            print("✅ 数据库连接成功!")
            cursor = connection.cursor(dictionary=True)
            
            # 提取2000个账号
            extract_query = "SELECT * FROM `邮箱系统` LIMIT 4000"
            cursor.execute(extract_query)
            accounts = cursor.fetchall()
            
            if not accounts:
                print("❌ 未找到任何账号")
                return False
                
            print(f"✅ 成功提取 {len(accounts)} 个账号")
            
            # 打印真实邮箱数据
            print("\n真实邮箱数据:")
            print("="*30)
            for account in accounts:
                if '邮箱' in account:
                    print(f"邮箱: {account['邮箱']}")
            print("="*30 + "\n")
            
            # 创建DataFrame
            df = pd.DataFrame(accounts)
            
            # 获取提取的ID列表，用于删除
            account_ids = [account['id'] for account in accounts if 'id' in account]
            
            # 删除这些账号
            if account_ids:
                placeholders = ', '.join(['%s'] * len(account_ids))
                delete_query = f"DELETE FROM `邮箱系统` WHERE id IN ({placeholders})"
                cursor.execute(delete_query, account_ids)
                connection.commit()
                print(f"✅ 已删除 {cursor.rowcount} 个账号")
            else:
                print("⚠️ 无法获取账号ID，跳过删除操作")
            
            # 从DataFrame中排除不需要的列
            columns_to_exclude = ['id', '创建时间', '人数']
            for col in columns_to_exclude:
                if col in df.columns:
                    df = df.drop(columns=[col])
                    print(f"ℹ️ 已从输出中排除列: {col}")
            
            # 将"邮箱"列的数据统一为指定值
            if '邮箱' in df.columns:
                df['邮箱'] = "<EMAIL>"
                print("ℹ️ 已将'邮箱'列的所有数据统一替换为: <EMAIL>")
            
            # 保存到桌面Excel文件
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_file = os.path.join(desktop_path, f"邮箱系统账号_{timestamp}.xlsx")
            
            df.to_excel(excel_file, index=False)
            print(f"✅ 已将账号数据保存到: {excel_file}")
            
            cursor.close()
            
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return False
        
    finally:
        if connection and connection.is_connected():
            connection.close()
            print("\n数据库连接已关闭")
            
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("邮箱系统账号提取与删除工具")
    print("=" * 60)
    
    success = extract_and_delete_accounts()
    
    if success:
        print("✅ 操作完成")
    else:
        print("❌ 操作失败") 
2025-06-19 15:35:01,307 - INFO - 开始执行卡密到期时间增加操作...
2025-06-19 15:35:01,308 - INFO - 正在连接数据库...
2025-06-19 15:35:01,575 - INFO - ✅ 数据库连接成功!
2025-06-19 15:35:01,592 - INFO - 检查'卡密系统'表是否存在...
2025-06-19 15:35:01,610 - INFO - ✅ '卡密系统'表存在
2025-06-19 15:35:01,610 - INFO - 检查'到期时间'字段是否存在...
2025-06-19 15:35:01,627 - INFO - ✅ '到期时间'字段存在
2025-06-19 15:35:01,628 - INFO - 查询卡密系统表记录总数...
2025-06-19 15:35:01,646 - INFO - 📊 卡密系统表总记录数: 4,601 条
2025-06-19 15:35:01,647 - INFO - 查询有到期时间的记录数量...
2025-06-19 15:35:01,664 - INFO - 📊 有到期时间的记录数: 4,601 条
2025-06-19 15:35:01,664 - INFO - 查看更新前的示例记录...
2025-06-19 15:35:01,680 - ERROR - ❌ 数据库操作失败: 1054 (42S22): Unknown column 'id' in 'field list'
2025-06-19 15:35:01,697 - INFO - 🔄 事务已回滚
2025-06-19 15:35:01,714 - INFO - 🔌 数据库连接已关闭
2025-06-19 15:37:53,528 - INFO - 开始执行卡密到期时间增加操作...
2025-06-19 15:37:53,530 - INFO - 正在连接数据库...
2025-06-19 15:37:53,848 - INFO - ✅ 数据库连接成功!
2025-06-19 15:37:53,866 - INFO - 检查'卡密系统'表是否存在...
2025-06-19 15:37:53,884 - INFO - ✅ '卡密系统'表存在
2025-06-19 15:37:53,885 - INFO - 检查'到期时间'字段是否存在...
2025-06-19 15:37:53,903 - INFO - ✅ '到期时间'字段存在
2025-06-19 15:37:53,904 - INFO - 查询卡密系统表记录总数...
2025-06-19 15:37:53,922 - INFO - 📊 卡密系统表总记录数: 4,601 条
2025-06-19 15:37:53,923 - INFO - 查询有到期时间的记录数量...
2025-06-19 15:37:53,942 - INFO - 📊 有到期时间的记录数: 4,601 条
2025-06-19 15:37:53,943 - INFO - 查看更新前的示例记录...
2025-06-19 15:37:53,961 - INFO - 📋 更新前的示例记录:
2025-06-19 15:37:53,962 - INFO -    记录1: 到期时间: 2025-05-31 17:01:53
2025-06-19 15:37:53,962 - INFO -    记录2: 到期时间: 2025-05-31 21:30:06
2025-06-19 15:37:53,963 - INFO -    记录3: 到期时间: 2025-05-31 21:44:05
2025-06-19 15:37:53,964 - INFO -    记录4: 到期时间: 2025-06-01 08:48:12
2025-06-19 15:37:53,964 - INFO -    记录5: 到期时间: 2025-06-02 06:58:17
2025-06-19 15:37:53,965 - INFO - 开始执行到期时间增加操作...
2025-06-19 15:37:53,965 - INFO - 执行SQL: 
                UPDATE `卡密系统`
                SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
                WHERE `到期时间` IS NOT NULL
            
2025-06-19 15:37:54,012 - INFO - ✅ 更新完成! 影响记录数: 4,601 条
2025-06-19 15:37:54,012 - INFO - ⏱️ 执行时间: 0.046 秒
2025-06-19 15:37:54,013 - INFO - 提交事务...
2025-06-19 15:37:54,034 - INFO - ✅ 事务提交成功!
2025-06-19 15:37:54,035 - INFO - 验证更新结果...
2025-06-19 15:37:54,052 - INFO - 📋 更新后的示例记录:
2025-06-19 15:37:54,053 - INFO -    记录1: 到期时间: 2025-06-01 17:01:53
2025-06-19 15:37:54,054 - INFO -    记录2: 到期时间: 2025-06-01 21:30:06
2025-06-19 15:37:54,054 - INFO -    记录3: 到期时间: 2025-06-01 21:44:05
2025-06-19 15:37:54,054 - INFO -    记录4: 到期时间: 2025-06-02 08:48:12
2025-06-19 15:37:54,054 - INFO -    记录5: 到期时间: 2025-06-03 06:58:17
2025-06-19 15:37:54,055 - INFO - 
============================================================
2025-06-19 15:37:54,055 - INFO - 📈 操作统计信息:
2025-06-19 15:37:54,055 - INFO -    表名: 卡密系统
2025-06-19 15:37:54,056 - INFO -    到期时间字段: 到期时间
2025-06-19 15:37:54,056 - INFO -    总记录数: 4,601 条
2025-06-19 15:37:54,056 - INFO -    有到期时间记录数: 4,601 条
2025-06-19 15:37:54,057 - INFO -    实际更新记录数: 4,601 条
2025-06-19 15:37:54,057 - INFO -    执行时间: 0.046 秒
2025-06-19 15:37:54,058 - INFO -    操作时间: 2025-06-19 15:37:54
2025-06-19 15:37:54,058 - INFO - ============================================================
2025-06-19 15:37:54,074 - INFO - 🔌 数据库连接已关闭
2025-07-28 18:07:07,591 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 18:07:07,592 - INFO - 正在连接数据库...
2025-07-28 18:13:30,040 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 18:13:30,040 - INFO - 正在连接数据库...
2025-07-28 18:21:02,170 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 18:21:02,171 - INFO - 正在连接数据库...服务器: **************:3306
2025-07-28 18:21:02,171 - DEBUG - 数据库连接参数: {'host': '**************', 'port': 3306, 'user': 'root', 'database': 'kami3162', 'charset': 'utf8mb4', 'autocommit': False, 'connection_timeout': 10}
2025-07-28 18:23:50,426 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 18:23:50,427 - INFO - 正在连接数据库...服务器: **************:3306
2025-07-28 18:23:50,427 - DEBUG - 数据库连接参数: {'host': '**************', 'port': 3306, 'user': 'root', 'database': 'kami3162', 'charset': 'utf8mb4', 'autocommit': False, 'connection_timeout': 10}
2025-07-28 18:40:58,344 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 18:40:58,345 - INFO - 正在连接数据库...
2025-07-28 18:45:38,646 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 18:45:38,646 - INFO - 正在连接数据库...
2025-07-28 19:56:05,368 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 19:56:05,368 - INFO - 正在连接数据库...
2025-07-28 20:07:32,076 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 20:07:32,077 - INFO - 正在连接数据库...
2025-07-28 20:09:43,866 - INFO - 开始执行卡密到期时间增加操作...
2025-07-28 20:09:43,866 - INFO - 正在连接数据库...
2025-07-29 08:52:49,339 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 08:52:49,340 - INFO - 正在连接数据库...
2025-07-29 15:12:56,935 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:12:56,936 - INFO - 正在连接数据库...
2025-07-29 15:13:12,385 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:13:12,386 - INFO - 正在连接数据库...
2025-07-29 15:16:10,833 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:16:10,834 - INFO - 正在连接数据库...
2025-07-29 15:16:13,399 - ERROR - ❌ 数据库操作失败: 2003 (HY000): Can't connect to MySQL server on '43.165.184.49:3306' (10061)
2025-07-29 15:27:08,339 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:27:08,340 - INFO - 正在连接数据库...
2025-07-29 15:27:11,051 - ERROR - ❌ 数据库操作失败: 2003 (HY000): Can't connect to MySQL server on '43.165.184.49:3306' (10061)
2025-07-29 15:30:12,027 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:30:12,028 - INFO - 正在连接数据库...
2025-07-29 15:30:26,259 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:30:26,260 - INFO - 正在连接数据库...
2025-07-29 15:46:23,118 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:46:23,118 - INFO - 正在连接数据库...
2025-07-29 15:49:35,348 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:49:35,348 - INFO - 正在连接数据库 119.45.250.178... (尝试 1/3)
2025-07-29 15:52:42,912 - INFO - ============================================================
2025-07-29 15:52:42,913 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:52:42,914 - INFO - 正在连接数据库 119.45.250.178...
2025-07-29 15:52:43,048 - INFO - ✅ 数据库连接成功!
2025-07-29 15:52:43,049 - INFO - 创建数据库游标...
2025-07-29 15:52:43,050 - INFO - 检查'卡密系统'表是否存在...
2025-07-29 15:52:43,078 - INFO - ✅ '卡密系统'表存在
2025-07-29 15:52:43,079 - INFO - 查询有到期时间记录数...
2025-07-29 15:52:43,107 - INFO - 📊 有到期时间的记录数: 8,102 条
2025-07-29 15:52:43,107 - INFO - 查看更新前的示例记录...
2025-07-29 15:52:43,136 - INFO - 更新前的示例记录:
2025-07-29 15:52:43,137 - INFO -   记录1: 2021-08-01 17:26:36
2025-07-29 15:52:43,137 - INFO -   记录2: 2024-01-04 17:18:01
2025-07-29 15:52:43,137 - INFO -   记录3: 2024-07-27 22:32:08
2025-07-29 15:52:43,137 - INFO -   记录4: 2024-07-31 16:59:00
2025-07-29 15:52:43,137 - INFO -   记录5: 2024-08-02 14:59:39
2025-07-29 15:52:43,138 - INFO - 执行更新操作...
2025-07-29 15:52:43,288 - INFO - ✅ SQL执行完成，影响行数: 8102 条
2025-07-29 15:52:43,289 - INFO - ⏱️ 执行时间: 0.150 秒
2025-07-29 15:52:43,290 - INFO - 提交事务...
2025-07-29 15:52:43,352 - INFO - ✅ 事务已提交
2025-07-29 15:52:43,353 - INFO - 查看更新后的示例记录...
2025-07-29 15:52:43,382 - INFO - 更新后的示例记录:
2025-07-29 15:52:43,383 - INFO -   记录1: 2021-08-02 17:26:36
2025-07-29 15:52:43,383 - INFO -   记录2: 2024-01-05 17:18:01
2025-07-29 15:52:43,383 - INFO -   记录3: 2024-07-28 22:32:08
2025-07-29 15:52:43,384 - INFO -   记录4: 2024-08-01 16:59:00
2025-07-29 15:52:43,384 - INFO -   记录5: 2024-08-03 14:59:39
2025-07-29 15:52:43,385 - INFO - ============================================================
2025-07-29 15:52:43,385 - INFO - ✅ 卡密到期时间增加操作完成!
2025-07-29 15:52:43,385 - INFO - ============================================================
2025-07-29 15:52:43,385 - INFO - 🔌 数据库连接已关闭
2025-07-29 15:53:46,331 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 15:53:46,333 - INFO - 正在连接数据库 119.45.250.178... (尝试 1/3)
2025-07-29 16:05:21,206 - INFO - ============================================================
2025-07-29 16:05:21,208 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 16:05:21,208 - INFO - 正在连接数据库 119.45.250.178:3306...
2025-07-29 16:05:21,340 - INFO - ✅ 数据库连接成功!
2025-07-29 16:05:21,341 - INFO - 创建数据库游标...
2025-07-29 16:05:21,342 - INFO - 检查'卡密系统'表是否存在...
2025-07-29 16:05:21,368 - INFO - ✅ '卡密系统'表存在
2025-07-29 16:05:21,369 - INFO - 检查'到期时间'字段是否存在...
2025-07-29 16:05:21,395 - INFO - ✅ '到期时间'字段存在
2025-07-29 16:05:21,395 - INFO - 查询卡密系统表记录总数...
2025-07-29 16:05:21,427 - INFO - 📊 卡密系统表总记录数: 8,102 条
2025-07-29 16:05:21,428 - INFO - 查询有到期时间的记录数量...
2025-07-29 16:05:21,455 - INFO - 📊 有到期时间的记录数: 8,102 条
2025-07-29 16:05:21,455 - INFO - 查看更新前的示例记录...
2025-07-29 16:05:21,483 - INFO - 📋 更新前的示例记录:
2025-07-29 16:05:21,483 - INFO -    记录1: 到期时间: 2021-08-02 17:26:36
2025-07-29 16:05:21,483 - INFO -    记录2: 到期时间: 2024-01-05 17:18:01
2025-07-29 16:05:21,484 - INFO -    记录3: 到期时间: 2024-07-28 22:32:08
2025-07-29 16:05:21,484 - INFO -    记录4: 到期时间: 2024-08-01 16:59:00
2025-07-29 16:05:21,484 - INFO -    记录5: 到期时间: 2024-08-03 14:59:39
2025-07-29 16:05:21,484 - INFO - 开始执行到期时间增加操作...
2025-07-29 16:05:21,485 - INFO - 执行SQL: 
            UPDATE `卡密系统`
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
            WHERE `到期时间` IS NOT NULL
        
2025-07-29 16:05:21,608 - INFO - ✅ 更新完成! 影响记录数: 8,102 条
2025-07-29 16:05:21,609 - INFO - ⏱️ 执行时间: 0.123 秒
2025-07-29 16:05:21,609 - INFO - 提交事务...
2025-07-29 16:05:21,662 - INFO - ✅ 事务提交成功!
2025-07-29 16:05:21,662 - INFO - 验证更新结果...
2025-07-29 16:05:21,690 - INFO - 📋 更新后的示例记录:
2025-07-29 16:05:21,691 - INFO -    记录1: 到期时间: 2021-08-03 17:26:36
2025-07-29 16:05:21,692 - INFO -    记录2: 到期时间: 2024-01-06 17:18:01
2025-07-29 16:05:21,693 - INFO -    记录3: 到期时间: 2024-07-29 22:32:08
2025-07-29 16:05:21,693 - INFO -    记录4: 到期时间: 2024-08-02 16:59:00
2025-07-29 16:05:21,693 - INFO -    记录5: 到期时间: 2024-08-04 14:59:39
2025-07-29 16:05:21,694 - INFO - 
============================================================
2025-07-29 16:05:21,695 - INFO - 📈 操作统计信息:
2025-07-29 16:05:21,695 - INFO -    表名: 卡密系统
2025-07-29 16:05:21,695 - INFO -    到期时间字段: 到期时间
2025-07-29 16:05:21,696 - INFO -    总记录数: 8,102 条
2025-07-29 16:05:21,696 - INFO -    有到期时间记录数: 8,102 条
2025-07-29 16:05:21,696 - INFO -    实际更新记录数: 8,102 条
2025-07-29 16:05:21,697 - INFO -    执行时间: 0.123 秒
2025-07-29 16:05:21,697 - INFO -    操作时间: 2025-07-29 16:05:21
2025-07-29 16:05:21,697 - INFO - ============================================================
2025-07-29 16:05:21,698 - INFO - 🔌 数据库连接已关闭
2025-07-29 16:07:40,500 - INFO - ============================================================
2025-07-29 16:07:40,502 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 16:07:40,503 - INFO - 正在连接数据库 119.45.250.178:3306...
2025-07-29 16:07:40,640 - INFO - ✅ 数据库连接成功!
2025-07-29 16:07:40,641 - INFO - 创建数据库游标...
2025-07-29 16:07:40,641 - INFO - 检查'卡密系统'表是否存在...
2025-07-29 16:07:40,667 - INFO - ✅ '卡密系统'表存在
2025-07-29 16:07:40,668 - INFO - 检查'到期时间'字段是否存在...
2025-07-29 16:07:40,695 - INFO - ✅ '到期时间'字段存在
2025-07-29 16:07:40,695 - INFO - 查询卡密系统表记录总数...
2025-07-29 16:07:40,723 - INFO - 📊 卡密系统表总记录数: 8,102 条
2025-07-29 16:07:40,723 - INFO - 查询有到期时间的记录数量...
2025-07-29 16:07:40,751 - INFO - 📊 有到期时间的记录数: 8,102 条
2025-07-29 16:07:40,751 - INFO - 查看更新前的示例记录...
2025-07-29 16:07:40,780 - INFO - 📋 更新前的示例记录:
2025-07-29 16:07:40,780 - INFO -    记录1: 到期时间: 2021-08-03 17:26:36
2025-07-29 16:07:40,781 - INFO -    记录2: 到期时间: 2024-01-06 17:18:01
2025-07-29 16:07:40,781 - INFO -    记录3: 到期时间: 2024-07-29 22:32:08
2025-07-29 16:07:40,781 - INFO -    记录4: 到期时间: 2024-08-02 16:59:00
2025-07-29 16:07:40,781 - INFO -    记录5: 到期时间: 2024-08-04 14:59:39
2025-07-29 16:07:40,781 - INFO - 开始执行到期时间增加操作...
2025-07-29 16:07:40,781 - INFO - 执行SQL: 
            UPDATE `卡密系统`
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
            WHERE `到期时间` IS NOT NULL
        
2025-07-29 16:07:40,945 - INFO - ✅ 更新完成! 影响记录数: 8,102 条
2025-07-29 16:07:40,947 - INFO - ⏱️ 执行时间: 0.163 秒
2025-07-29 16:07:40,948 - INFO - 提交事务...
2025-07-29 16:07:40,998 - INFO - ✅ 事务提交成功!
2025-07-29 16:07:40,999 - INFO - 验证更新结果...
2025-07-29 16:07:41,027 - INFO - 📋 更新后的示例记录:
2025-07-29 16:07:41,028 - INFO -    记录1: 到期时间: 2021-08-04 17:26:36
2025-07-29 16:07:41,028 - INFO -    记录2: 到期时间: 2024-01-07 17:18:01
2025-07-29 16:07:41,028 - INFO -    记录3: 到期时间: 2024-07-30 22:32:08
2025-07-29 16:07:41,028 - INFO -    记录4: 到期时间: 2024-08-03 16:59:00
2025-07-29 16:07:41,028 - INFO -    记录5: 到期时间: 2024-08-05 14:59:39
2025-07-29 16:07:41,029 - INFO - 
============================================================
2025-07-29 16:07:41,029 - INFO - 📈 操作统计信息:
2025-07-29 16:07:41,029 - INFO -    表名: 卡密系统
2025-07-29 16:07:41,029 - INFO -    到期时间字段: 到期时间
2025-07-29 16:07:41,029 - INFO -    总记录数: 8,102 条
2025-07-29 16:07:41,029 - INFO -    有到期时间记录数: 8,102 条
2025-07-29 16:07:41,029 - INFO -    实际更新记录数: 8,102 条
2025-07-29 16:07:41,030 - INFO -    执行时间: 0.163 秒
2025-07-29 16:07:41,030 - INFO -    操作时间: 2025-07-29 16:07:41
2025-07-29 16:07:41,030 - INFO - ============================================================
2025-07-29 16:07:41,030 - INFO - 🔌 数据库连接已关闭
2025-07-29 22:20:19,521 - INFO - ============================================================
2025-07-29 22:20:19,522 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 22:20:19,522 - INFO - 正在连接数据库 119.45.250.178:3306...
2025-07-29 22:20:19,653 - INFO - ✅ 数据库连接成功!
2025-07-29 22:20:19,654 - INFO - 创建数据库游标...
2025-07-29 22:20:19,655 - INFO - 检查'卡密系统'表是否存在...
2025-07-29 22:20:19,683 - INFO - ✅ '卡密系统'表存在
2025-07-29 22:20:19,684 - INFO - 检查'到期时间'字段是否存在...
2025-07-29 22:20:19,710 - INFO - ✅ '到期时间'字段存在
2025-07-29 22:20:19,711 - INFO - 查询卡密系统表记录总数...
2025-07-29 22:20:19,738 - INFO - 📊 卡密系统表总记录数: 8,184 条
2025-07-29 22:20:19,739 - INFO - 查询有到期时间的记录数量...
2025-07-29 22:20:19,765 - INFO - 📊 有到期时间的记录数: 8,184 条
2025-07-29 22:20:19,766 - INFO - 查看更新前的示例记录...
2025-07-29 22:20:19,793 - INFO - 📋 更新前的示例记录:
2025-07-29 22:20:19,793 - INFO -    记录1: 到期时间: 2021-08-01 17:26:36
2025-07-29 22:20:19,794 - INFO -    记录2: 到期时间: 2024-01-04 17:18:01
2025-07-29 22:20:19,794 - INFO -    记录3: 到期时间: 2024-07-27 22:32:08
2025-07-29 22:20:19,794 - INFO -    记录4: 到期时间: 2024-07-31 16:59:00
2025-07-29 22:20:19,795 - INFO -    记录5: 到期时间: 2024-08-02 14:59:39
2025-07-29 22:20:19,795 - INFO - 开始执行到期时间增加操作...
2025-07-29 22:20:19,795 - INFO - 执行SQL: 
            UPDATE `卡密系统`
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
            WHERE `到期时间` IS NOT NULL
        
2025-07-29 22:20:19,944 - INFO - ✅ 更新完成! 影响记录数: 8,184 条
2025-07-29 22:20:19,945 - INFO - ⏱️ 执行时间: 0.149 秒
2025-07-29 22:20:19,945 - INFO - 提交事务...
2025-07-29 22:20:20,000 - INFO - ✅ 事务提交成功!
2025-07-29 22:20:20,000 - INFO - 验证更新结果...
2025-07-29 22:20:20,027 - INFO - 📋 更新后的示例记录:
2025-07-29 22:20:20,028 - INFO -    记录1: 到期时间: 2021-08-02 17:26:36
2025-07-29 22:20:20,028 - INFO -    记录2: 到期时间: 2024-01-05 17:18:01
2025-07-29 22:20:20,028 - INFO -    记录3: 到期时间: 2024-07-28 22:32:08
2025-07-29 22:20:20,028 - INFO -    记录4: 到期时间: 2024-08-01 16:59:00
2025-07-29 22:20:20,029 - INFO -    记录5: 到期时间: 2024-08-03 14:59:39
2025-07-29 22:20:20,029 - INFO - 
============================================================
2025-07-29 22:20:20,029 - INFO - 📈 操作统计信息:
2025-07-29 22:20:20,029 - INFO -    表名: 卡密系统
2025-07-29 22:20:20,029 - INFO -    到期时间字段: 到期时间
2025-07-29 22:20:20,029 - INFO -    总记录数: 8,184 条
2025-07-29 22:20:20,030 - INFO -    有到期时间记录数: 8,184 条
2025-07-29 22:20:20,030 - INFO -    实际更新记录数: 8,184 条
2025-07-29 22:20:20,030 - INFO -    执行时间: 0.149 秒
2025-07-29 22:20:20,030 - INFO -    操作时间: 2025-07-29 22:20:20
2025-07-29 22:20:20,030 - INFO - ============================================================
2025-07-29 22:20:20,031 - INFO - 🔌 数据库连接已关闭
2025-07-29 22:20:30,042 - INFO - ============================================================
2025-07-29 22:20:30,044 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 22:20:30,045 - INFO - 正在连接数据库 119.45.250.178:3306...
2025-07-29 22:20:30,183 - INFO - ✅ 数据库连接成功!
2025-07-29 22:20:30,184 - INFO - 创建数据库游标...
2025-07-29 22:20:30,184 - INFO - 检查'卡密系统'表是否存在...
2025-07-29 22:20:30,212 - INFO - ✅ '卡密系统'表存在
2025-07-29 22:20:30,213 - INFO - 检查'到期时间'字段是否存在...
2025-07-29 22:20:30,241 - INFO - ✅ '到期时间'字段存在
2025-07-29 22:20:30,241 - INFO - 查询卡密系统表记录总数...
2025-07-29 22:20:30,269 - INFO - 📊 卡密系统表总记录数: 8,184 条
2025-07-29 22:20:30,270 - INFO - 查询有到期时间的记录数量...
2025-07-29 22:20:30,299 - INFO - 📊 有到期时间的记录数: 8,184 条
2025-07-29 22:20:30,299 - INFO - 查看更新前的示例记录...
2025-07-29 22:20:30,328 - INFO - 📋 更新前的示例记录:
2025-07-29 22:20:30,328 - INFO -    记录1: 到期时间: 2021-08-02 17:26:36
2025-07-29 22:20:30,328 - INFO -    记录2: 到期时间: 2024-01-05 17:18:01
2025-07-29 22:20:30,328 - INFO -    记录3: 到期时间: 2024-07-28 22:32:08
2025-07-29 22:20:30,328 - INFO -    记录4: 到期时间: 2024-08-01 16:59:00
2025-07-29 22:20:30,328 - INFO -    记录5: 到期时间: 2024-08-03 14:59:39
2025-07-29 22:20:30,328 - INFO - 开始执行到期时间增加操作...
2025-07-29 22:20:30,328 - INFO - 执行SQL: 
            UPDATE `卡密系统`
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
            WHERE `到期时间` IS NOT NULL
        
2025-07-29 22:20:30,479 - INFO - ✅ 更新完成! 影响记录数: 8,184 条
2025-07-29 22:20:30,481 - INFO - ⏱️ 执行时间: 0.151 秒
2025-07-29 22:20:30,481 - INFO - 提交事务...
2025-07-29 22:20:30,532 - INFO - ✅ 事务提交成功!
2025-07-29 22:20:30,533 - INFO - 验证更新结果...
2025-07-29 22:20:30,562 - INFO - 📋 更新后的示例记录:
2025-07-29 22:20:30,562 - INFO -    记录1: 到期时间: 2021-08-03 17:26:36
2025-07-29 22:20:30,562 - INFO -    记录2: 到期时间: 2024-01-06 17:18:01
2025-07-29 22:20:30,562 - INFO -    记录3: 到期时间: 2024-07-29 22:32:08
2025-07-29 22:20:30,563 - INFO -    记录4: 到期时间: 2024-08-02 16:59:00
2025-07-29 22:20:30,563 - INFO -    记录5: 到期时间: 2024-08-04 14:59:39
2025-07-29 22:20:30,563 - INFO - 
============================================================
2025-07-29 22:20:30,563 - INFO - 📈 操作统计信息:
2025-07-29 22:20:30,563 - INFO -    表名: 卡密系统
2025-07-29 22:20:30,563 - INFO -    到期时间字段: 到期时间
2025-07-29 22:20:30,563 - INFO -    总记录数: 8,184 条
2025-07-29 22:20:30,563 - INFO -    有到期时间记录数: 8,184 条
2025-07-29 22:20:30,564 - INFO -    实际更新记录数: 8,184 条
2025-07-29 22:20:30,564 - INFO -    执行时间: 0.151 秒
2025-07-29 22:20:30,564 - INFO -    操作时间: 2025-07-29 22:20:30
2025-07-29 22:20:30,564 - INFO - ============================================================
2025-07-29 22:20:30,564 - INFO - 🔌 数据库连接已关闭
2025-07-29 22:20:33,708 - INFO - ============================================================
2025-07-29 22:20:33,709 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 22:20:33,711 - INFO - 正在连接数据库 119.45.250.178:3306...
2025-07-29 22:20:33,856 - INFO - ✅ 数据库连接成功!
2025-07-29 22:20:33,857 - INFO - 创建数据库游标...
2025-07-29 22:20:33,858 - INFO - 检查'卡密系统'表是否存在...
2025-07-29 22:20:33,886 - INFO - ✅ '卡密系统'表存在
2025-07-29 22:20:33,887 - INFO - 检查'到期时间'字段是否存在...
2025-07-29 22:20:33,917 - INFO - ✅ '到期时间'字段存在
2025-07-29 22:20:33,918 - INFO - 查询卡密系统表记录总数...
2025-07-29 22:20:33,948 - INFO - 📊 卡密系统表总记录数: 8,184 条
2025-07-29 22:20:33,948 - INFO - 查询有到期时间的记录数量...
2025-07-29 22:20:33,977 - INFO - 📊 有到期时间的记录数: 8,184 条
2025-07-29 22:20:33,977 - INFO - 查看更新前的示例记录...
2025-07-29 22:20:34,007 - INFO - 📋 更新前的示例记录:
2025-07-29 22:20:34,007 - INFO -    记录1: 到期时间: 2021-08-03 17:26:36
2025-07-29 22:20:34,007 - INFO -    记录2: 到期时间: 2024-01-06 17:18:01
2025-07-29 22:20:34,008 - INFO -    记录3: 到期时间: 2024-07-29 22:32:08
2025-07-29 22:20:34,008 - INFO -    记录4: 到期时间: 2024-08-02 16:59:00
2025-07-29 22:20:34,008 - INFO -    记录5: 到期时间: 2024-08-04 14:59:39
2025-07-29 22:20:34,009 - INFO - 开始执行到期时间增加操作...
2025-07-29 22:20:34,009 - INFO - 执行SQL: 
            UPDATE `卡密系统`
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
            WHERE `到期时间` IS NOT NULL
        
2025-07-29 22:20:34,145 - INFO - ✅ 更新完成! 影响记录数: 8,184 条
2025-07-29 22:20:34,146 - INFO - ⏱️ 执行时间: 0.136 秒
2025-07-29 22:20:34,148 - INFO - 提交事务...
2025-07-29 22:20:34,206 - INFO - ✅ 事务提交成功!
2025-07-29 22:20:34,207 - INFO - 验证更新结果...
2025-07-29 22:20:34,244 - INFO - 📋 更新后的示例记录:
2025-07-29 22:20:34,245 - INFO -    记录1: 到期时间: 2021-08-04 17:26:36
2025-07-29 22:20:34,246 - INFO -    记录2: 到期时间: 2024-01-07 17:18:01
2025-07-29 22:20:34,247 - INFO -    记录3: 到期时间: 2024-07-30 22:32:08
2025-07-29 22:20:34,247 - INFO -    记录4: 到期时间: 2024-08-03 16:59:00
2025-07-29 22:20:34,248 - INFO -    记录5: 到期时间: 2024-08-05 14:59:39
2025-07-29 22:20:34,248 - INFO - 
============================================================
2025-07-29 22:20:34,248 - INFO - 📈 操作统计信息:
2025-07-29 22:20:34,249 - INFO -    表名: 卡密系统
2025-07-29 22:20:34,249 - INFO -    到期时间字段: 到期时间
2025-07-29 22:20:34,250 - INFO -    总记录数: 8,184 条
2025-07-29 22:20:34,250 - INFO -    有到期时间记录数: 8,184 条
2025-07-29 22:20:34,250 - INFO -    实际更新记录数: 8,184 条
2025-07-29 22:20:34,251 - INFO -    执行时间: 0.136 秒
2025-07-29 22:20:34,251 - INFO -    操作时间: 2025-07-29 22:20:34
2025-07-29 22:20:34,252 - INFO - ============================================================
2025-07-29 22:20:34,253 - INFO - 🔌 数据库连接已关闭
2025-07-29 23:01:03,371 - INFO - ============================================================
2025-07-29 23:01:03,372 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 23:01:03,373 - INFO - 正在连接数据库 119.45.250.178:3306...
2025-07-29 23:01:03,521 - INFO - ✅ 数据库连接成功!
2025-07-29 23:01:03,522 - INFO - 创建数据库游标...
2025-07-29 23:01:03,522 - INFO - 检查'卡密系统'表是否存在...
2025-07-29 23:01:03,552 - INFO - ✅ '卡密系统'表存在
2025-07-29 23:01:03,553 - INFO - 检查'到期时间'字段是否存在...
2025-07-29 23:01:03,582 - INFO - ✅ '到期时间'字段存在
2025-07-29 23:01:03,582 - INFO - 查询卡密系统表记录总数...
2025-07-29 23:01:03,613 - INFO - 📊 卡密系统表总记录数: 8,184 条
2025-07-29 23:01:03,613 - INFO - 查询有到期时间的记录数量...
2025-07-29 23:01:03,644 - INFO - 📊 有到期时间的记录数: 8,184 条
2025-07-29 23:01:03,644 - INFO - 查看更新前的示例记录...
2025-07-29 23:01:03,676 - INFO - 📋 更新前的示例记录:
2025-07-29 23:01:03,677 - INFO -    记录1: 到期时间: 2021-08-04 17:26:36
2025-07-29 23:01:03,677 - INFO -    记录2: 到期时间: 2024-01-07 17:18:01
2025-07-29 23:01:03,678 - INFO -    记录3: 到期时间: 2024-07-30 22:32:08
2025-07-29 23:01:03,678 - INFO -    记录4: 到期时间: 2024-08-03 16:59:00
2025-07-29 23:01:03,678 - INFO -    记录5: 到期时间: 2024-08-05 14:59:39
2025-07-29 23:01:03,679 - INFO - 开始执行到期时间增加操作...
2025-07-29 23:01:03,679 - INFO - 执行SQL: 
            UPDATE `卡密系统`
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
            WHERE `到期时间` IS NOT NULL
        
2025-07-29 23:01:03,818 - INFO - ✅ 更新完成! 影响记录数: 8,184 条
2025-07-29 23:01:03,819 - INFO - ⏱️ 执行时间: 0.138 秒
2025-07-29 23:01:03,820 - INFO - 提交事务...
2025-07-29 23:01:03,871 - INFO - ✅ 事务提交成功!
2025-07-29 23:01:03,872 - INFO - 验证更新结果...
2025-07-29 23:01:03,904 - INFO - 📋 更新后的示例记录:
2025-07-29 23:01:03,905 - INFO -    记录1: 到期时间: 2021-08-05 17:26:36
2025-07-29 23:01:03,905 - INFO -    记录2: 到期时间: 2024-01-08 17:18:01
2025-07-29 23:01:03,905 - INFO -    记录3: 到期时间: 2024-07-31 22:32:08
2025-07-29 23:01:03,906 - INFO -    记录4: 到期时间: 2024-08-04 16:59:00
2025-07-29 23:01:03,906 - INFO -    记录5: 到期时间: 2024-08-06 14:59:39
2025-07-29 23:01:03,907 - INFO - 
============================================================
2025-07-29 23:01:03,907 - INFO - 📈 操作统计信息:
2025-07-29 23:01:03,907 - INFO -    表名: 卡密系统
2025-07-29 23:01:03,907 - INFO -    到期时间字段: 到期时间
2025-07-29 23:01:03,907 - INFO -    总记录数: 8,184 条
2025-07-29 23:01:03,907 - INFO -    有到期时间记录数: 8,184 条
2025-07-29 23:01:03,908 - INFO -    实际更新记录数: 8,184 条
2025-07-29 23:01:03,908 - INFO -    执行时间: 0.138 秒
2025-07-29 23:01:03,908 - INFO -    操作时间: 2025-07-29 23:01:03
2025-07-29 23:01:03,908 - INFO - ============================================================
2025-07-29 23:01:03,908 - INFO - 🔌 数据库连接已关闭
2025-07-29 23:01:08,948 - INFO - ============================================================
2025-07-29 23:01:08,950 - INFO - 开始执行卡密到期时间增加操作...
2025-07-29 23:01:08,951 - INFO - 正在连接数据库 119.45.250.178:3306...
2025-07-29 23:01:09,070 - INFO - ✅ 数据库连接成功!
2025-07-29 23:01:09,071 - INFO - 创建数据库游标...
2025-07-29 23:01:09,072 - INFO - 检查'卡密系统'表是否存在...
2025-07-29 23:01:09,098 - INFO - ✅ '卡密系统'表存在
2025-07-29 23:01:09,099 - INFO - 检查'到期时间'字段是否存在...
2025-07-29 23:01:09,125 - INFO - ✅ '到期时间'字段存在
2025-07-29 23:01:09,126 - INFO - 查询卡密系统表记录总数...
2025-07-29 23:01:09,153 - INFO - 📊 卡密系统表总记录数: 8,184 条
2025-07-29 23:01:09,154 - INFO - 查询有到期时间的记录数量...
2025-07-29 23:01:09,180 - INFO - 📊 有到期时间的记录数: 8,184 条
2025-07-29 23:01:09,180 - INFO - 查看更新前的示例记录...
2025-07-29 23:01:09,207 - INFO - 📋 更新前的示例记录:
2025-07-29 23:01:09,208 - INFO -    记录1: 到期时间: 2021-08-05 17:26:36
2025-07-29 23:01:09,208 - INFO -    记录2: 到期时间: 2024-01-08 17:18:01
2025-07-29 23:01:09,208 - INFO -    记录3: 到期时间: 2024-07-31 22:32:08
2025-07-29 23:01:09,208 - INFO -    记录4: 到期时间: 2024-08-04 16:59:00
2025-07-29 23:01:09,209 - INFO -    记录5: 到期时间: 2024-08-06 14:59:39
2025-07-29 23:01:09,209 - INFO - 开始执行到期时间增加操作...
2025-07-29 23:01:09,209 - INFO - 执行SQL: 
            UPDATE `卡密系统`
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
            WHERE `到期时间` IS NOT NULL
        
2025-07-29 23:01:09,350 - INFO - ✅ 更新完成! 影响记录数: 8,184 条
2025-07-29 23:01:09,351 - INFO - ⏱️ 执行时间: 0.141 秒
2025-07-29 23:01:09,351 - INFO - 提交事务...
2025-07-29 23:01:09,399 - INFO - ✅ 事务提交成功!
2025-07-29 23:01:09,399 - INFO - 验证更新结果...
2025-07-29 23:01:09,426 - INFO - 📋 更新后的示例记录:
2025-07-29 23:01:09,426 - INFO -    记录1: 到期时间: 2021-08-06 17:26:36
2025-07-29 23:01:09,427 - INFO -    记录2: 到期时间: 2024-01-09 17:18:01
2025-07-29 23:01:09,427 - INFO -    记录3: 到期时间: 2024-08-01 22:32:08
2025-07-29 23:01:09,427 - INFO -    记录4: 到期时间: 2024-08-05 16:59:00
2025-07-29 23:01:09,428 - INFO -    记录5: 到期时间: 2024-08-07 14:59:39
2025-07-29 23:01:09,428 - INFO - 
============================================================
2025-07-29 23:01:09,428 - INFO - 📈 操作统计信息:
2025-07-29 23:01:09,428 - INFO -    表名: 卡密系统
2025-07-29 23:01:09,428 - INFO -    到期时间字段: 到期时间
2025-07-29 23:01:09,428 - INFO -    总记录数: 8,184 条
2025-07-29 23:01:09,428 - INFO -    有到期时间记录数: 8,184 条
2025-07-29 23:01:09,428 - INFO -    实际更新记录数: 8,184 条
2025-07-29 23:01:09,429 - INFO -    执行时间: 0.141 秒
2025-07-29 23:01:09,429 - INFO -    操作时间: 2025-07-29 23:01:09
2025-07-29 23:01:09,429 - INFO - ============================================================
2025-07-29 23:01:09,429 - INFO - 🔌 数据库连接已关闭

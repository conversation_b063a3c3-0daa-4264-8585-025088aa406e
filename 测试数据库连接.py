#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接
"""

import mysql.connector
from mysql.connector import Error
import time
import sys
import traceback
import socket

def 打印分隔线():
    print("-" * 60)

def 测试数据库连接(重试次数=3):
    """测试数据库连接是否正常"""
    
    # 数据库配置 - 使用火山云配置
    数据库配置 = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'connection_timeout': 30,  # 增加超时时间到30秒
        'buffered': True
    }
    
    已尝试次数 = 0
    
    while 已尝试次数 < 重试次数:
        已尝试次数 += 1
        
        try:
            打印分隔线()
            print(f"正在尝试连接数据库... (第 {已尝试次数}/{重试次数} 次)")
            print(f"连接参数: 主机={数据库配置['host']}, 端口={数据库配置['port']}, 数据库={数据库配置['database']}")
            
            # 首先测试端口是否开放
            打印分隔线()
            print(f"测试端口 {数据库配置['host']}:{数据库配置['port']} 是否开放...")
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(5)
            
            try:
                result = s.connect_ex((数据库配置['host'], 数据库配置['port']))
                if result == 0:
                    print(f"✅ 端口 {数据库配置['port']} 已开放")
                else:
                    print(f"❌ 端口 {数据库配置['port']} 未开放 (错误代码: {result})")
                    if 已尝试次数 < 重试次数:
                        print(f"⏳ 等待 3 秒后重试...")
                        time.sleep(3)
                        continue
                    else:
                        return False
            finally:
                s.close()
            
            # 开始连接数据库
            打印分隔线()
            print("开始连接数据库...")
            开始时间 = time.time()
            连接 = mysql.connector.connect(**数据库配置)
            连接时间 = time.time() - 开始时间
            print(f"连接耗时: {连接时间:.2f} 秒")
            
            if 连接.is_connected():
                打印分隔线()
                db_info = 连接.get_server_info()
                print(f"✅ 数据库连接成功! MySQL 服务器版本: {db_info}")
                
                游标 = 连接.cursor()
                游标.execute("SELECT DATABASE()")
                数据库名 = 游标.fetchone()[0]
                print(f"✅ 当前连接的数据库: {数据库名}")
                
                # 测试执行简单查询
                打印分隔线()
                print("测试执行简单查询 SHOW TABLES...")
                游标.execute("SHOW TABLES")
                表列表 = 游标.fetchall()
                print(f"✅ 数据库中的表: {len(表列表)} 个")
                for i, 表 in enumerate(表列表[:5], 1):
                    print(f"  - 表 {i}: {表[0]}")
                if len(表列表) > 5:
                    print(f"  ... (还有 {len(表列表)-5} 个表)")
                
                # 检查是否存在卡密系统表
                打印分隔线()
                print("检查'卡密系统'表是否存在...")
                游标.execute("SHOW TABLES LIKE '卡密系统'")
                表存在 = 游标.fetchone()
                
                if 表存在:
                    print("✅ '卡密系统'表存在")
                    
                    # 检查到期时间字段
                    print("检查'到期时间'字段是否存在...")
                    游标.execute("DESCRIBE `卡密系统`")
                    字段信息 = 游标.fetchall()
                    字段名列表 = [字段[0] for 字段 in 字段信息]
                    
                    if "到期时间" in 字段名列表:
                        print("✅ '到期时间'字段存在")
                    else:
                        print("❌ '到期时间'字段不存在")
                        print(f"可用字段: {', '.join(字段名列表)}")
                else:
                    print("❌ '卡密系统'表不存在")
                
                打印分隔线()
                游标.close()
                连接.close()
                print("🔌 数据库连接已关闭")
                print("✅ 数据库连接测试成功!")
                return True
        
        except Error as e:
            打印分隔线()
            print(f"❌ 数据库连接失败: {e}")
            if 已尝试次数 < 重试次数:
                print(f"⏳ 等待 3 秒后重试...")
                time.sleep(3)
            else:
                print(f"❌ 已达到最大重试次数 ({重试次数})，连接失败。")
                return False
        
        except Exception as e:
            打印分隔线()
            print(f"❌ 发生未知错误: {e}")
            print("错误详情:")
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("="*60)
    print("测试数据库连接")
    print("="*60)
    
    try:
        测试数据库连接()
    except KeyboardInterrupt:
        print("\n\n❌ 测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生异常: {e}")
        traceback.print_exc()
    finally:
        print("="*60)
        print("测试结束") 
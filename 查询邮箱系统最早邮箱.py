#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询邮箱系统表中创建时间最早的邮箱记录
"""

import mysql.connector
from mysql.connector import Error
from datetime import datetime

def 查询最早邮箱():
    """查询邮箱系统表中创建时间最早的邮箱"""
    
    # 数据库配置
    数据库配置 = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    连接 = None
    
    try:
        print("正在连接数据库...")
        连接 = mysql.connector.connect(**数据库配置)
        
        if 连接.is_connected():
            print("✅ 数据库连接成功!")
            
            游标 = 连接.cursor()
            
            print("\n" + "="*60)
            print("查询邮箱系统表中创建时间最早的邮箱")
            print("="*60)
            
            # 1. 查询总记录数
            print("1. 📊 获取表基本信息...")
            游标.execute("SELECT COUNT(*) FROM `邮箱系统`")
            总记录数 = 游标.fetchone()[0]
            print(f"   邮箱系统表总记录数: {总记录数:,} 条")
            
            if 总记录数 == 0:
                print("❌ 邮箱系统表中没有数据")
                return
            
            # 2. 查询创建时间最早的记录
            print("\n2. 🔍 查询创建时间最早的邮箱...")
            查询SQL = """
                SELECT id, 邮箱, 创建时间, 人数
                FROM `邮箱系统`
                ORDER BY 创建时间 ASC
                LIMIT 1
            """
            
            游标.execute(查询SQL)
            最早记录 = 游标.fetchone()
            
            if 最早记录:
                id, 邮箱, 创建时间, 人数 = 最早记录
                
                print(f"\n📋 查询结果:")
                print(f"   ID: {id}")
                print(f"   邮箱: {邮箱}")
                print(f"   创建时间: {创建时间}")
                print(f"   人数: {人数}")
                
                # 计算距离现在的时间
                if isinstance(创建时间, datetime):
                    现在时间 = datetime.now()
                    时间差 = 现在时间 - 创建时间
                    天数 = 时间差.days
                    小时数 = 时间差.seconds // 3600
                    分钟数 = (时间差.seconds % 3600) // 60
                    
                    print(f"\n⏰ 时间统计:")
                    print(f"   距离现在: {天数} 天 {小时数} 小时 {分钟数} 分钟")
                    print(f"   创建日期: {创建时间.strftime('%Y年%m月%d日')}")
                    print(f"   创建时刻: {创建时间.strftime('%H:%M:%S')}")
            else:
                print("❌ 未找到记录")
            
            # 3. 查询前10个最早的记录
            print(f"\n3. 📋 前10个最早创建的邮箱:")
            查询前10SQL = """
                SELECT id, 邮箱, 创建时间, 人数
                FROM `邮箱系统`
                ORDER BY 创建时间 ASC
                LIMIT 10
            """
            
            游标.execute(查询前10SQL)
            前10记录 = 游标.fetchall()
            
            if 前10记录:
                print(f"   {'序号':<4} {'ID':<8} {'邮箱':<30} {'创建时间':<20} {'人数':<6}")
                print(f"   {'-'*4} {'-'*8} {'-'*30} {'-'*20} {'-'*6}")
                
                for 序号, (id, 邮箱, 创建时间, 人数) in enumerate(前10记录, 1):
                    创建时间_格式化 = 创建时间.strftime('%Y-%m-%d %H:%M:%S') if isinstance(创建时间, datetime) else str(创建时间)
                    邮箱_显示 = 邮箱[:28] + '..' if len(邮箱) > 30 else 邮箱
                    print(f"   {序号:<4} {id:<8} {邮箱_显示:<30} {创建时间_格式化:<20} {人数:<6}")
            
            # 4. 查询创建时间的统计信息
            print(f"\n4. 📈 创建时间统计信息:")
            
            # 最早和最晚时间
            游标.execute("SELECT MIN(创建时间), MAX(创建时间) FROM `邮箱系统`")
            最早时间, 最晚时间 = 游标.fetchone()
            
            if 最早时间 and 最晚时间:
                print(f"   最早创建时间: {最早时间}")
                print(f"   最晚创建时间: {最晚时间}")
                
                if isinstance(最早时间, datetime) and isinstance(最晚时间, datetime):
                    时间跨度 = 最晚时间 - 最早时间
                    跨度天数 = 时间跨度.days
                    跨度小时 = 时间跨度.seconds // 3600
                    print(f"   时间跨度: {跨度天数} 天 {跨度小时} 小时")
            
            # 按日期统计前5天的创建数量
            print(f"\n5. 📊 最早几天的创建统计:")
            按日期统计SQL = """
                SELECT DATE(创建时间) as 日期, COUNT(*) as 数量
                FROM `邮箱系统`
                ORDER BY 创建时间 ASC
                LIMIT 1000
            """
            
            游标.execute(按日期统计SQL)
            所有记录 = 游标.fetchall()
            
            # 手动按日期分组统计
            日期统计 = {}
            for 记录 in 所有记录:
                日期, _ = 记录
                if 日期 in 日期统计:
                    日期统计[日期] += 1
                else:
                    日期统计[日期] = 1
            
            # 显示前5天的统计
            排序日期 = sorted(日期统计.keys())[:5]
            
            print(f"   {'日期':<12} {'创建数量':<8}")
            print(f"   {'-'*12} {'-'*8}")
            
            for 日期 in 排序日期:
                数量 = 日期统计[日期]
                print(f"   {日期:<12} {数量:<8}")
            
            游标.close()
            
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        
    finally:
        if 连接 and 连接.is_connected():
            连接.close()
            print(f"\n数据库连接已关闭")

if __name__ == "__main__":
    print("=" * 60)
    print("邮箱系统表最早记录查询工具")
    print("=" * 60)
    
    查询最早邮箱()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡密系统到期时间增加一天
对数据库中"卡密系统"表的所有卡密的到期时间增加一天
"""

import pymysql
from datetime import datetime, timedelta
import logging
import time
import traceback
import sys

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('卡密到期时间更新.log', encoding='utf8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def 增加卡密到期时间():
    """对卡密系统表中所有卡密的到期时间增加一天"""
    
    # 数据库配置 - 使用火山云配置
    数据库配置 = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'db': 'kami3162',  # 使用db而不是database（pymysql参数）
        'charset': 'utf8mb4',
        'connect_timeout': 30
    }
    
    logger = setup_logging()
    连接 = None
    
    try:
        logger.info("="*60)
        logger.info("开始执行卡密到期时间增加操作...")
        
        # 连接数据库
        logger.info(f"正在连接数据库 {数据库配置['host']}:{数据库配置['port']}...")
        
        try:
            连接 = pymysql.connect(**数据库配置)
            logger.info("✅ 数据库连接成功!")
        except Exception as conn_err:
            logger.error(f"❌ 数据库连接失败: {conn_err}")
            logger.error(traceback.format_exc())
            return False
        
        # 创建游标
        logger.info("创建数据库游标...")
        游标 = 连接.cursor()
        
        # 1. 首先检查"卡密系统"表是否存在
        logger.info("检查'卡密系统'表是否存在...")
        游标.execute("SHOW TABLES LIKE '卡密系统'")
        表存在 = 游标.fetchone()
        
        if not 表存在:
            logger.error("❌ '卡密系统'表不存在!")
            return False
        
        logger.info("✅ '卡密系统'表存在")
        
        # 2. 检查"到期时间"字段是否存在
        logger.info("检查'到期时间'字段是否存在...")
        游标.execute("DESCRIBE `卡密系统`")
        字段信息 = 游标.fetchall()
        字段名列表 = [字段[0] for 字段 in 字段信息]

        到期时间字段 = "到期时间"

        if 到期时间字段 not in 字段名列表:
            logger.error(f"❌ '到期时间'字段不存在!")
            logger.error(f"可用字段: {', '.join(字段名列表)}")
            return False

        logger.info(f"✅ '到期时间'字段存在")
        
        # 3. 查询当前记录总数
        logger.info("查询卡密系统表记录总数...")
        游标.execute("SELECT COUNT(*) FROM `卡密系统`")
        总记录数 = 游标.fetchone()[0]
        logger.info(f"📊 卡密系统表总记录数: {总记录数:,} 条")
        
        if 总记录数 == 0:
            logger.warning("⚠️ 卡密系统表中没有数据")
            return True
        
        # 4. 查询有到期时间的记录数量
        logger.info("查询有到期时间的记录数量...")
        游标.execute("SELECT COUNT(*) FROM `卡密系统` WHERE `到期时间` IS NOT NULL")
        有到期时间记录数 = 游标.fetchone()[0]
        logger.info(f"📊 有到期时间的记录数: {有到期时间记录数:,} 条")
        
        if 有到期时间记录数 == 0:
            logger.warning("⚠️ 没有记录包含到期时间数据")
            return True
        
        # 5. 显示一些示例记录（更新前）
        logger.info("查看更新前的示例记录...")
        游标.execute("""
            SELECT `到期时间`
            FROM `卡密系统`
            WHERE `到期时间` IS NOT NULL
            ORDER BY `到期时间` ASC
            LIMIT 5
        """)
        示例记录 = 游标.fetchall()

        logger.info("📋 更新前的示例记录:")
        for i, 记录 in enumerate(示例记录, 1):
            logger.info(f"   记录{i}: 到期时间: {记录[0]}")

        # 6. 执行更新操作
        logger.info("开始执行到期时间增加操作...")

        # 使用DATE_ADD函数增加一天
        更新SQL = """
            UPDATE `卡密系统`
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY)
            WHERE `到期时间` IS NOT NULL
        """

        logger.info(f"执行SQL: {更新SQL}")
        
        # 执行更新
        开始时间 = datetime.now()
        游标.execute(更新SQL)
        更新数量 = 游标.rowcount
        执行时间 = datetime.now() - 开始时间
        
        logger.info(f"✅ 更新完成! 影响记录数: {更新数量:,} 条")
        logger.info(f"⏱️ 执行时间: {执行时间.total_seconds():.3f} 秒")
        
        # 7. 提交事务
        logger.info("提交事务...")
        连接.commit()
        logger.info("✅ 事务提交成功!")
        
        # 8. 验证更新结果
        logger.info("验证更新结果...")
        游标.execute("""
            SELECT `到期时间`
            FROM `卡密系统`
            WHERE `到期时间` IS NOT NULL
            ORDER BY `到期时间` ASC
            LIMIT 5
        """)
        更新后示例 = 游标.fetchall()

        logger.info("📋 更新后的示例记录:")
        for i, 记录 in enumerate(更新后示例, 1):
            logger.info(f"   记录{i}: 到期时间: {记录[0]}")

        # 9. 统计信息
        logger.info("\n" + "="*60)
        logger.info("📈 操作统计信息:")
        logger.info(f"   表名: 卡密系统")
        logger.info(f"   到期时间字段: 到期时间")
        logger.info(f"   总记录数: {总记录数:,} 条")
        logger.info(f"   有到期时间记录数: {有到期时间记录数:,} 条")
        logger.info(f"   实际更新记录数: {更新数量:,} 条")
        logger.info(f"   执行时间: {执行时间.total_seconds():.3f} 秒")
        logger.info(f"   操作时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("="*60)
        
        # 关闭游标
        游标.close()
        return True
            
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        logger.error("错误详细信息:")
        logger.error(traceback.format_exc())
        
        if 连接:
            try:
                连接.rollback()
                logger.info("🔄 事务已回滚")
            except:
                pass
        return False
        
    finally:
        if 连接:
            try:
                连接.close()
                logger.info("🔌 数据库连接已关闭")
            except:
                pass

def main():
    """主函数"""
    print("="*60)
    print("卡密系统到期时间增加一天工具")
    print("="*60)
    
    # 确认操作
    确认 = input("⚠️  此操作将对所有卡密的到期时间增加一天，是否继续？(y/N): ")
    
    if 确认.lower() not in ['y', 'yes', '是']:
        print("❌ 操作已取消")
        return
    
    # 执行操作
    成功 = 增加卡密到期时间()
    
    if 成功:
        print("\n🎉 卡密到期时间增加操作完成!")
    else:
        print("\n❌ 操作失败，请检查日志文件")

if __name__ == "__main__":
    main()

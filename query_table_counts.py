#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询指定表的记录总数
"""

import mysql.connector
from mysql.connector import Error

def query_table_counts():
    """查询邮箱系统和邮箱免费表的记录总数"""

    # 数据库配置
    config = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': True
    }

    connection = None

    try:
        print("正在连接数据库...")
        connection = mysql.connector.connect(**config)

        if connection.is_connected():
            print("✅ 数据库连接成功!")
            cursor = connection.cursor()

            # 要查询的表名列表
            tables_to_query = ['邮箱系统', '邮箱免费']

            print("\n" + "="*50)
            print("表记录总数查询结果")
            print("="*50)

            for table_name in tables_to_query:
                try:
                    # 查询表的记录总数
                    query = f"SELECT COUNT(*) FROM `{table_name}`"
                    cursor.execute(query)
                    count = cursor.fetchone()[0]

                    print(f"📊 表 '{table_name}': {count:,} 条记录")

                except Error as e:
                    print(f"❌ 查询表 '{table_name}' 失败: {e}")
                    print()

            # 查询总体统计
            print("-"*50)
            total_records = 0
            for table_name in tables_to_query:
                try:
                    query = f"SELECT COUNT(*) FROM `{table_name}`"
                    cursor.execute(query)
                    count = cursor.fetchone()[0]
                    total_records += count
                except:
                    pass

            print(f"📈 两个表的记录总和: {total_records:,} 条")
            print("-"*50)

            cursor.close()

    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False

    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return False

    finally:
        if connection and connection.is_connected():
            connection.close()
            print("\n数据库连接已关闭")

    return True

if __name__ == "__main__":
    print("=" * 60)
    print("邮箱表记录总数查询工具")
    print("=" * 60)

    success = query_table_counts()

    if success:
        print("✅ 查询完成")
    else:
        print("❌ 查询失败")

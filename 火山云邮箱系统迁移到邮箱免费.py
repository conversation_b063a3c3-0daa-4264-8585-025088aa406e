#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同库数据迁移工具 - 在**************数据库内迁移数据
从"邮箱系统"表迁移1万条数据到"邮箱免费"表
"""

import pymysql
import time
import traceback
import sys

def 同库数据迁移():
    """在同一数据库内从邮箱系统表迁移1万条数据到邮箱免费表"""
    
    # 数据库配置
    数据库配置 = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'db': 'kami3162',  # pymysql使用db而不是database
        'charset': 'utf8mb4',
        'connect_timeout': 30
    }

    # 迁移数量
    迁移数量 = 1000
    
    数据库连接 = None
    
    try:
        print("正在连接数据库...")
        数据库连接 = pymysql.connect(**数据库配置)
        
        # 设置自动提交为False以启用事务
        数据库连接.autocommit(False)
        
        print("✅ 数据库连接成功!")
        
        游标 = 数据库连接.cursor()
        
        print("\n" + "="*60)
        print("同库数据迁移工具执行")
        print(f"从 '邮箱系统' 迁移到 '邮箱免费'")
        print(f"迁移数量: {迁移数量:,} 条")
        print("="*60)
        
        # 1. 获取数据状态
        print("1. 📊 检查数据状态...")
        游标.execute("SELECT COUNT(*) FROM `邮箱系统`")
        源数据总量 = 游标.fetchone()[0]
        
        游标.execute("SELECT COUNT(*) FROM `邮箱免费`")
        目标数据量_迁移前 = 游标.fetchone()[0]
        
        print(f"   邮箱系统表: {源数据总量:,} 条")
        print(f"   邮箱免费表: {目标数据量_迁移前:,} 条")
        
        if 源数据总量 == 0:
            print("❌ 邮箱系统表中没有数据需要迁移")
            return False
        
        # 确定实际迁移数量
        实际迁移数量 = min(迁移数量, 源数据总量)
        
        print(f"\n2. 📋 迁移计划:")
        print(f"   计划迁移: {迁移数量:,} 条")
        print(f"   实际迁移: {实际迁移数量:,} 条")
        
        # 3. 获取表结构信息
        print("\n3. 🔍 获取表结构...")
        游标.execute("DESCRIBE `邮箱系统`")
        字段信息 = 游标.fetchall()
        
        # 构建列名列表
        字段名列表 = [字段[0] for 字段 in 字段信息]
        字段字符串 = ", ".join([f"`{字段}`" for 字段 in 字段名列表])
        占位符 = ", ".join(["%s"] * len(字段名列表))
        
        print(f"   表字段数: {len(字段名列表)} 个")
        print(f"   字段列表: {', '.join(字段名列表[:5])}{'...' if len(字段名列表) > 5 else ''}")
        
        # 4. 确认迁移
        print(f"\n⚠️  即将迁移 {实际迁移数量:,} 条数据")
        print(f"   数据库: {数据库配置['host']}")
        print(f"   源表: 邮箱系统")
        print(f"   目标表: 邮箱免费")
        
        确认 = input("\n确认执行迁移? (输入 'YES' 确认): ").strip()
        
        if 确认 != 'YES':
            print("❌ 迁移已取消")
            return False
        
        # 5. 开始迁移
        print(f"\n🚀 开始执行数据迁移...")
        开始时间 = time.time()
        
        # 分批处理，每批1000条
        批处理大小 = 1000
        总迁移数量 = 0
        已迁移ID列表 = []
        
        print(f"   使用批处理模式，每批 {批处理大小} 条")
        
        for 偏移量 in range(0, 实际迁移数量, 批处理大小):
            当前批次大小 = min(批处理大小, 实际迁移数量 - 偏移量)
            
            print(f"\n   处理批次 {偏移量//批处理大小 + 1}: {偏移量 + 1} - {偏移量 + 当前批次大小}")
            
            # 获取一批数据
            批次开始时间 = time.time()
            游标.execute(f"SELECT {字段字符串} FROM `邮箱系统` LIMIT %s OFFSET %s", 
                        (当前批次大小, 偏移量))
            批次数据 = 游标.fetchall()
            
            if not 批次数据:
                print("   ⚠️  没有更多数据")
                break
            
            # 插入到邮箱免费表
            插入查询 = f"INSERT INTO `邮箱免费` ({字段字符串}) VALUES ({占位符})"
            游标.executemany(插入查询, 批次数据)
            
            # 记录已迁移的数据ID（假设第一列是ID）
            if 字段名列表[0].lower() in ['id', 'email_id', 'mail_id']:
                批次ID列表 = [行[0] for 行 in 批次数据]
                已迁移ID列表.extend(批次ID列表)
            
            总迁移数量 += len(批次数据)
            批次耗时 = time.time() - 批次开始时间
            
            print(f"   ✅ 批次完成: {len(批次数据)} 条 (耗时: {批次耗时:.3f}秒)")
            
            # 显示进度
            进度 = (总迁移数量 / 实际迁移数量) * 100
            print(f"   📈 总进度: {总迁移数量:,}/{实际迁移数量:,} ({进度:.1f}%)")
        
        # 6. 删除邮箱系统表中已迁移的数据
        print(f"\n🗑️  删除邮箱系统表中已迁移的数据...")
        删除开始时间 = time.time()
        
        if 已迁移ID列表 and 字段名列表[0].lower() in ['id', 'email_id', 'mail_id']:
            # 分批删除
            删除批处理大小 = 1000
            总删除数量 = 0
            
            for i in range(0, len(已迁移ID列表), 删除批处理大小):
                批次ID = 已迁移ID列表[i:i + 删除批处理大小]
                删除占位符 = ", ".join(["%s"] * len(批次ID))
                
                删除查询 = f"DELETE FROM `邮箱系统` WHERE `{字段名列表[0]}` IN ({删除占位符})"
                游标.execute(删除查询, 批次ID)
                
                总删除数量 += 游标.rowcount
                
                if (i // 删除批处理大小 + 1) % 5 == 0:
                    print(f"   删除进度: {总删除数量:,}/{len(已迁移ID列表):,}")
            
            删除耗时 = time.time() - 删除开始时间
            print(f"   ✅ 删除完成: {总删除数量:,} 条 (耗时: {删除耗时:.3f}秒)")
        else:
            # 如果无法通过ID删除，则删除前N条记录
            游标.execute(f"DELETE FROM `邮箱系统` LIMIT %s", (总迁移数量,))
            删除耗时 = time.time() - 删除开始时间
            print(f"   ✅ 删除完成: {游标.rowcount:,} 条 (耗时: {删除耗时:.3f}秒)")
        
        # 7. 提交事务
        print("   步骤: 提交事务...")
        数据库连接.commit()
        print("   ✅ 事务提交成功")
        
        # 8. 最终验证
        print(f"\n📋 最终验证...")
        
        游标.execute("SELECT COUNT(*) FROM `邮箱系统`")
        源数据量_迁移后 = 游标.fetchone()[0]
        
        游标.execute("SELECT COUNT(*) FROM `邮箱免费`")
        目标数据量_迁移后 = 游标.fetchone()[0]
        
        总耗时 = time.time() - 开始时间
        
        print(f"\n" + "="*60)
        print("🎉 同库数据迁移完成!")
        print("="*60)
        
        print(f"📊 迁移结果:")
        print(f"   邮箱系统表: {源数据总量:,} → {源数据量_迁移后:,} 条")
        print(f"   邮箱免费表: {目标数据量_迁移前:,} → {目标数据量_迁移后:,} 条")
        print(f"   实际迁移数量: {总迁移数量:,} 条")
        
        print(f"\n⏱️  性能统计:")
        print(f"   总耗时: {总耗时:.3f}秒")
        print(f"   平均速度: {总迁移数量/总耗时:.0f} 条/秒")
        
        # 验证数据完整性
        期望目标数量 = 目标数据量_迁移前 + 总迁移数量
        期望源数量 = 源数据总量 - 总迁移数量
        
        if 目标数据量_迁移后 == 期望目标数量 and 源数据量_迁移后 == 期望源数量:
            print(f"\n✅ 迁移成功!")
            print(f"   ✅ 数据完整性验证通过")
            print(f"   ✅ {总迁移数量:,} 条数据已成功从 '邮箱系统' 迁移到 '邮箱免费'")
        else:
            print(f"\n⚠️  数据验证异常")
            print(f"   期望源表数量: {期望源数量:,}, 实际: {源数据量_迁移后:,}")
            print(f"   期望目标数量: {期望目标数量:,}, 实际: {目标数据量_迁移后:,}")
        
        游标.close()
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
        if 数据库连接:
            try:
                数据库连接.rollback()
                print("🔄 事务已回滚")
            except:
                pass
        return False
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        print("错误详情:")
        traceback.print_exc()
        
        if 数据库连接:
            try:
                数据库连接.rollback()
                print("🔄 事务已回滚")
            except:
                pass
        return False
        
    finally:
        if 数据库连接:
            try:
                数据库连接.close()
                print("\n数据库连接已关闭")
            except:
                pass
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("同库数据迁移工具")
    print("从**************数据库的'邮箱系统'迁移1万条数据到'邮箱免费'")
    print("=" * 60)
    
    try:
        成功 = 同库数据迁移()
        
        if 成功:
            print("\n🎉 同库数据迁移成功完成!")
        else:
            print("\n❌ 同库数据迁移失败或被取消")
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序执行失败: {e}")
        traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表字段检查和数据迁移脚本
检查两个数据库中"卡密系统"表的字段结构，并设计迁移方案
"""

import pymysql
import pandas as pd
from typing import Dict, List, Tuple, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
源数据库配置 = {
    'host': '***************',
    'port': 3306,
    'user': 'root',
    'password': 'YU6709',
    'database': 'kami3162',
    'charset': 'utf8mb4'
}

目标数据库配置 = {
    'host': '**************',
    'port': 3306,
    'user': 'root',
    'password': 'Yuyu6709.',
    'database': 'kami3162',
    'charset': 'utf8mb4'
}

class DatabaseMigrationChecker:
    def __init__(self, source_config: Dict, target_config: Dict):
        self.source_config = source_config
        self.target_config = target_config
        self.source_conn = None
        self.target_conn = None
        
    def connect_databases(self):
        """连接到源数据库和目标数据库"""
        try:
            logger.info("正在连接源数据库...")
            self.source_conn = pymysql.connect(**self.source_config)
            logger.info("源数据库连接成功")
            
            logger.info("正在连接目标数据库...")
            self.target_conn = pymysql.connect(**self.target_config)
            logger.info("目标数据库连接成功")
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def get_table_structure(self, connection, table_name: str) -> List[Dict]:
        """获取表结构信息"""
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"DESCRIBE `{table_name}`")
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取表结构失败: {e}")
            return []
    
    def get_table_count(self, connection, table_name: str) -> int:
        """获取表记录数"""
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"获取表记录数失败: {e}")
            return 0
    
    def check_table_exists(self, connection, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                return cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"检查表存在性失败: {e}")
            return False
    
    def compare_table_structures(self, table_name: str = "卡密系统"):
        """比较两个数据库中表的结构"""
        logger.info(f"开始检查表 '{table_name}' 的结构...")
        
        # 检查表是否存在
        source_exists = self.check_table_exists(self.source_conn, table_name)
        target_exists = self.check_table_exists(self.target_conn, table_name)
        
        print(f"\n{'='*60}")
        print(f"表存在性检查:")
        print(f"源数据库 ({self.source_config['host']}) 中表 '{table_name}' 存在: {source_exists}")
        print(f"目标数据库 ({self.target_config['host']}) 中表 '{table_name}' 存在: {target_exists}")
        
        if not source_exists or not target_exists:
            logger.warning("有数据库中不存在该表，无法进行结构比较")
            return None, None, None
        
        # 获取表结构
        source_structure = self.get_table_structure(self.source_conn, table_name)
        target_structure = self.get_table_structure(self.target_conn, table_name)
        
        # 获取记录数
        source_count = self.get_table_count(self.source_conn, table_name)
        target_count = self.get_table_count(self.target_conn, table_name)
        
        print(f"\n记录数统计:")
        print(f"源数据库记录数: {source_count}")
        print(f"目标数据库记录数: {target_count}")
        
        # 显示表结构
        print(f"\n{'='*60}")
        print(f"源数据库 ({self.source_config['host']}) 表结构:")
        self.print_table_structure(source_structure)
        
        print(f"\n{'='*60}")
        print(f"目标数据库 ({self.target_config['host']}) 表结构:")
        self.print_table_structure(target_structure)
        
        return source_structure, target_structure, (source_count, target_count)
    
    def print_table_structure(self, structure: List[Dict]):
        """打印表结构"""
        if not structure:
            print("无法获取表结构")
            return
            
        print(f"{'字段名':<20} {'类型':<20} {'是否为空':<10} {'键':<10} {'默认值':<15} {'额外':<15}")
        print("-" * 90)
        for field in structure:
            print(f"{field['Field']:<20} {field['Type']:<20} {field['Null']:<10} "
                  f"{field['Key']:<10} {str(field['Default']):<15} {field['Extra']:<15}")
    
    def analyze_migration_plan(self, source_structure: List[Dict], target_structure: List[Dict]):
        """分析迁移方案"""
        if not source_structure or not target_structure:
            return
            
        print(f"\n{'='*60}")
        print("迁移方案分析:")
        
        # 获取字段名列表
        source_fields = {field['Field']: field for field in source_structure}
        target_fields = {field['Field']: field for field in target_structure}
        
        # 找出共同字段
        common_fields = set(source_fields.keys()) & set(target_fields.keys())
        source_only_fields = set(source_fields.keys()) - set(target_fields.keys())
        target_only_fields = set(target_fields.keys()) - set(source_fields.keys())
        
        print(f"\n共同字段 ({len(common_fields)} 个):")
        for field in sorted(common_fields):
            source_type = source_fields[field]['Type']
            target_type = target_fields[field]['Type']
            compatible = "✓" if source_type == target_type else "⚠"
            print(f"  {compatible} {field:<20} 源:{source_type:<20} 目标:{target_type}")
        
        print(f"\n仅源数据库有的字段 ({len(source_only_fields)} 个):")
        for field in sorted(source_only_fields):
            print(f"  ✗ {field:<20} {source_fields[field]['Type']}")
        
        print(f"\n仅目标数据库有的字段 ({len(target_only_fields)} 个):")
        for field in sorted(target_only_fields):
            print(f"  + {field:<20} {target_fields[field]['Type']}")
        
        # 生成迁移SQL
        self.generate_migration_sql(common_fields, source_fields, target_fields)
    
    def generate_migration_sql(self, common_fields: set, source_fields: Dict, target_fields: Dict):
        """生成迁移SQL语句"""
        print(f"\n{'='*60}")
        print("建议的迁移SQL语句:")
        
        if not common_fields:
            print("没有共同字段，无法生成迁移SQL")
            return
        
        # 生成字段列表
        field_list = ", ".join([f"`{field}`" for field in sorted(common_fields)])
        
        print(f"\n-- 数据迁移SQL (仅迁移共同字段)")
        print(f"INSERT INTO `卡密系统` ({field_list})")
        print(f"SELECT {field_list}")
        print(f"FROM source_database.`卡密系统`;")
        
        print(f"\n-- 或者使用完整的迁移脚本:")
        print(f"-- 1. 先清空目标表 (可选，谨慎操作)")
        print(f"-- TRUNCATE TABLE `卡密系统`;")
        print(f"-- 2. 迁移数据")
        print(f"-- INSERT INTO `卡密系统` ({field_list})")
        print(f"-- SELECT {field_list} FROM source_database.`卡密系统`;")
    
    def close_connections(self):
        """关闭数据库连接"""
        if self.source_conn:
            self.source_conn.close()
            logger.info("源数据库连接已关闭")
        if self.target_conn:
            self.target_conn.close()
            logger.info("目标数据库连接已关闭")

def main():
    """主函数"""
    checker = DatabaseMigrationChecker(源数据库配置, 目标数据库配置)
    
    try:
        # 连接数据库
        checker.connect_databases()
        
        # 比较表结构
        source_structure, target_structure, counts = checker.compare_table_structures("卡密系统")
        
        # 分析迁移方案
        if source_structure and target_structure:
            checker.analyze_migration_plan(source_structure, target_structure)
        
        print(f"\n{'='*60}")
        print("检查完成！")
        
    except Exception as e:
        logger.error(f"执行过程中出现错误: {e}")
    finally:
        checker.close_connections()

if __name__ == "__main__":
    main()

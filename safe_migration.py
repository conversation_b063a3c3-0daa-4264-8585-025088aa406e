#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全数据迁移 - 使用锁表确保数据一致性
"""

import mysql.connector
from mysql.connector import Error
import time

def safe_migration():
    """安全的数据迁移，使用锁表"""
    
    # 数据库配置
    config = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'database': 'kami3162',
        'charset': 'utf8mb4',
        'autocommit': False
    }
    
    connection = None
    
    try:
        print("正在连接数据库...")
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ 数据库连接成功!")
            cursor = connection.cursor()
            
            print("\n" + "="*60)
            print("安全数据迁移执行")
            print("="*60)
            
            # 1. 锁定表以确保数据一致性
            print("1. 🔒 锁定相关表...")
            cursor.execute("LOCK TABLES `邮箱系统` READ, `邮箱免费` WRITE")
            print("   ✅ 表锁定成功")
            
            # 2. 获取准确的数据量
            cursor.execute("SELECT COUNT(*) FROM `邮箱系统`")
            source_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM `邮箱免费`")
            target_count_before = cursor.fetchone()[0]
            
            print(f"\n2. 📊 当前状态 (表已锁定):")
            print(f"   邮箱系统: {source_count:,} 条")
            print(f"   邮箱免费: {target_count_before:,} 条")
            
            if source_count == 0:
                print("❌ 邮箱系统表中没有数据需要迁移")
                cursor.execute("UNLOCK TABLES")
                return False
            
            # 3. 确认迁移
            print(f"\n⚠️  即将迁移 {source_count:,} 条数据")
            print("   注意: 表已锁定，其他操作将等待")
            
            confirm = input("\n确认执行迁移? (输入 'YES' 确认): ").strip()
            
            if confirm != 'YES':
                print("❌ 迁移已取消")
                cursor.execute("UNLOCK TABLES")
                return False
            
            # 4. 开始迁移
            print(f"\n🚀 开始执行数据迁移...")
            start_time = time.time()
            
            # 解锁表，开始事务
            cursor.execute("UNLOCK TABLES")
            print("   🔓 表已解锁，开始事务...")
            
            # 步骤1: 一次性插入所有数据
            print("   步骤1: 插入数据...")
            insert_start = time.time()
            
            cursor.execute("INSERT INTO `邮箱免费` SELECT * FROM `邮箱系统`")
            insert_time = time.time() - insert_start
            
            print(f"   ✅ 插入完成 (耗时: {insert_time:.3f}秒)")
            
            # 步骤2: 删除源表数据
            print("   步骤2: 删除源表数据...")
            delete_start = time.time()
            
            cursor.execute("DELETE FROM `邮箱系统`")
            delete_time = time.time() - delete_start
            
            print(f"   ✅ 删除完成 (耗时: {delete_time:.3f}秒)")
            
            # 5. 提交事务
            print("   步骤3: 提交事务...")
            connection.commit()
            print("   ✅ 事务提交成功")
            
            # 6. 最终验证
            print(f"\n📋 最终验证...")
            
            cursor.execute("SELECT COUNT(*) FROM `邮箱系统`")
            source_count_after = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM `邮箱免费`")
            target_count_after = cursor.fetchone()[0]
            
            total_time = time.time() - start_time
            
            print(f"\n" + "="*60)
            print("🎉 迁移完成!")
            print("="*60)
            
            print(f"📊 迁移结果:")
            print(f"   邮箱系统: {source_count:,} → {source_count_after:,} 条")
            print(f"   邮箱免费: {target_count_before:,} → {target_count_after:,} 条")
            print(f"   迁移数量: {source_count:,} 条")
            
            print(f"\n⏱️  性能统计:")
            print(f"   插入耗时: {insert_time:.3f}秒")
            print(f"   删除耗时: {delete_time:.3f}秒")
            print(f"   总耗时: {total_time:.3f}秒")
            print(f"   处理速度: {source_count/total_time:.0f} 条/秒")
            
            # 验证数据完整性
            if source_count_after == 0:
                print(f"\n✅ 迁移成功!")
                print(f"   ✅ 源表已清空")
                print(f"   ✅ 所有数据已移动到邮箱免费表")
            else:
                print(f"\n⚠️  迁移可能不完整")
                print(f"   源表剩余: {source_count_after} 条")
            
            cursor.close()
            
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        if connection:
            try:
                cursor.execute("UNLOCK TABLES")
            except:
                pass
            try:
                connection.rollback()
                print("🔄 事务已回滚")
            except:
                pass
        return False
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
        if connection:
            try:
                cursor.execute("UNLOCK TABLES")
            except:
                pass
            try:
                connection.rollback()
                print("🔄 事务已回滚")
            except:
                pass
        return False
        
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        if connection:
            try:
                cursor.execute("UNLOCK TABLES")
            except:
                pass
            try:
                connection.rollback()
                print("🔄 事务已回滚")
            except:
                pass
        return False
        
    finally:
        if connection and connection.is_connected():
            connection.close()
            print("\n数据库连接已关闭")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("安全数据迁移工具")
    print("从 '邮箱系统' 移动到 '邮箱免费'")
    print("=" * 60)
    
    success = safe_migration()
    
    if success:
        print("\n🎉 迁移成功完成!")
    else:
        print("\n❌ 迁移失败或被取消")

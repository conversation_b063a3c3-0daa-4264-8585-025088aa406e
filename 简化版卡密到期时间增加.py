#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版卡密到期时间增加工具
使用pymysql库代替mysql.connector
"""

import pymysql
from datetime import datetime, timedelta
import logging
import time
import traceback
import sys

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('卡密到期时间更新.log', encoding='utf8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def 增加卡密到期时间():
    """对卡密系统表中所有卡密的到期时间增加一天"""
    
    # 数据库配置
    数据库配置 = {
        'host': '**************',
        'port': 3306,
        'user': 'root',
        'password': 'Yuyu6709.',
        'db': 'kami3162',
        'charset': 'utf8mb4',
        'connect_timeout': 30
    }
    
    logger = setup_logging()
    连接 = None
    
    try:
        logger.info("="*60)
        logger.info("开始执行卡密到期时间增加操作...")
        
        # 连接数据库
        logger.info(f"正在连接数据库 {数据库配置['host']}...")
        
        try:
            连接 = pymysql.connect(**数据库配置)
            logger.info("✅ 数据库连接成功!")
        except Exception as conn_err:
            logger.error(f"❌ 数据库连接失败: {conn_err}")
            return False
        
        # 创建游标
        logger.info("创建数据库游标...")
        游标 = 连接.cursor()
        
        # 1. 检查卡密系统表是否存在
        logger.info("检查'卡密系统'表是否存在...")
        游标.execute("SHOW TABLES LIKE '卡密系统'")
        表存在 = 游标.fetchone()
        
        if not 表存在:
            logger.error("❌ '卡密系统'表不存在!")
            return False
        
        logger.info("✅ '卡密系统'表存在")
        
        # 2. 查询有到期时间的记录数
        logger.info("查询有到期时间记录数...")
        游标.execute("SELECT COUNT(*) FROM `卡密系统` WHERE `到期时间` IS NOT NULL")
        有到期时间记录数 = 游标.fetchone()[0]
        logger.info(f"📊 有到期时间的记录数: {有到期时间记录数:,} 条")
        
        if 有到期时间记录数 == 0:
            logger.warning("⚠️ 没有记录包含到期时间数据")
            return True
        
        # 3. 查看更新前的示例记录
        logger.info("查看更新前的示例记录...")
        游标.execute("""
            SELECT `到期时间` 
            FROM `卡密系统` 
            WHERE `到期时间` IS NOT NULL 
            ORDER BY `到期时间` ASC 
            LIMIT 5
        """)
        示例记录 = 游标.fetchall()
        
        logger.info("更新前的示例记录:")
        for i, 记录 in enumerate(示例记录, 1):
            logger.info(f"  记录{i}: {记录[0]}")
        
        # 4. 执行更新
        logger.info("执行更新操作...")
        更新SQL = """
            UPDATE `卡密系统` 
            SET `到期时间` = DATE_ADD(`到期时间`, INTERVAL 1 DAY) 
            WHERE `到期时间` IS NOT NULL
        """
        
        开始时间 = datetime.now()
        游标.execute(更新SQL)
        更新数量 = 游标.rowcount
        执行时间 = datetime.now() - 开始时间
        
        logger.info(f"✅ SQL执行完成，影响行数: {更新数量} 条")
        logger.info(f"⏱️ 执行时间: {执行时间.total_seconds():.3f} 秒")
        
        # 5. 提交事务
        logger.info("提交事务...")
        连接.commit()
        logger.info("✅ 事务已提交")
        
        # 6. 查看更新后的示例记录
        logger.info("查看更新后的示例记录...")
        游标.execute("""
            SELECT `到期时间` 
            FROM `卡密系统` 
            WHERE `到期时间` IS NOT NULL 
            ORDER BY `到期时间` ASC 
            LIMIT 5
        """)
        更新后示例 = 游标.fetchall()
        
        logger.info("更新后的示例记录:")
        for i, 记录 in enumerate(更新后示例, 1):
            logger.info(f"  记录{i}: {记录[0]}")
        
        logger.info("="*60)
        logger.info("✅ 卡密到期时间增加操作完成!")
        logger.info("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 操作失败: {e}")
        logger.error("错误详情:")
        logger.error(traceback.format_exc())
        
        if 连接:
            try:
                连接.rollback()
                logger.info("🔄 事务已回滚")
            except:
                pass
        
        return False
        
    finally:
        if 连接:
            try:
                连接.close()
                logger.info("🔌 数据库连接已关闭")
            except:
                pass

if __name__ == "__main__":
    print("="*60)
    print("简化版卡密系统到期时间增加一天工具")
    print("="*60)
    
    # 确认操作
    确认 = input("⚠️  此操作将对所有卡密的到期时间增加一天，是否继续？(y/N): ")
    
    if 确认.lower() not in ['y', 'yes', '是']:
        print("❌ 操作已取消")
        sys.exit()
    
    # 执行操作
    成功 = 增加卡密到期时间()
    
    if 成功:
        print("\n🎉 卡密到期时间增加操作完成!")
    else:
        print("\n❌ 操作失败，请检查日志文件") 